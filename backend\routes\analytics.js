import express from 'express';
import fs from 'fs-extra';
import path from 'path';

const router = express.Router();

// Caminho para armazenar dados de analytics
const ANALYTICS_DIR = path.join(process.cwd(), 'data/analytics');
const ANALYTICS_FILE = path.join(ANALYTICS_DIR, 'landing-page.json');

// Função para garantir que o diretório e arquivo existem
async function ensureAnalyticsSetup() {
    await fs.ensureDir(ANALYTICS_DIR);

    if (!await fs.pathExists(ANALYTICS_FILE)) {
        await fs.writeJson(ANALYTICS_FILE, []);
    }
}

/**
 * Receber dados de analytics da landing page
 */
router.post('/landing-page', async (req, res) => {
    try {
        // Garantir que o setup está feito
        await ensureAnalyticsSetup();

        const analyticsData = {
            ...req.body,
            timestamp: new Date().toISOString(),
            ip: req.ip || req.connection.remoteAddress,
            userAgent: req.get('User-Agent')
        };

        // Ler dados existentes
        let existingData = [];
        try {
            existingData = await fs.readJson(ANALYTICS_FILE);
        } catch (error) {
            console.warn('Erro ao ler arquivo de analytics, criando novo:', error.message);
            existingData = [];
        }

        // Adicionar novo evento
        existingData.push(analyticsData);

        // Manter apenas os últimos 10000 eventos para evitar arquivo muito grande
        if (existingData.length > 10000) {
            existingData = existingData.slice(-10000);
        }

        // Salvar dados
        await fs.writeJson(ANALYTICS_FILE, existingData, { spaces: 2 });

        console.log(`📊 Analytics recebido: ${analyticsData.event} - ${analyticsData.sessionId}`);

        res.json({
            success: true,
            message: 'Analytics recebido com sucesso'
        });

    } catch (error) {
        console.error('Erro ao processar analytics:', error);
        res.status(500).json({
            success: false,
            message: 'Erro interno do servidor',
            error: error.message
        });
    }
});

/**
 * Obter estatísticas de analytics (apenas para admins)
 */
router.get('/landing-page/stats', async (req, res) => {
    try {
        // Garantir que o setup está feito
        await ensureAnalyticsSetup();

        // Ler dados de analytics
        let analyticsData = [];
        try {
            analyticsData = await fs.readJson(ANALYTICS_FILE);
        } catch (error) {
            analyticsData = [];
        }

        // Calcular estatísticas básicas
        const stats = {
            totalEvents: analyticsData.length,
            uniqueSessions: new Set(analyticsData.map(event => event.sessionId)).size,
            eventTypes: {},
            lastWeek: [],
            topPages: {},
            topReferrers: {}
        };

        // Contar tipos de eventos
        analyticsData.forEach(event => {
            stats.eventTypes[event.event] = (stats.eventTypes[event.event] || 0) + 1;
            
            // Contar páginas
            if (event.url) {
                stats.topPages[event.url] = (stats.topPages[event.url] || 0) + 1;
            }
            
            // Contar referrers
            if (event.referrer) {
                stats.topReferrers[event.referrer] = (stats.topReferrers[event.referrer] || 0) + 1;
            }
        });

        // Dados da última semana
        const oneWeekAgo = new Date();
        oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

        stats.lastWeek = analyticsData.filter(event => 
            new Date(event.timestamp) >= oneWeekAgo
        );

        res.json({
            success: true,
            stats: stats
        });

    } catch (error) {
        console.error('Erro ao obter estatísticas:', error);
        res.status(500).json({
            success: false,
            message: 'Erro interno do servidor',
            error: error.message
        });
    }
});

/**
 * Limpar dados antigos de analytics
 */
router.delete('/landing-page/cleanup', async (req, res) => {
    try {
        // Garantir que o setup está feito
        await ensureAnalyticsSetup();

        const { daysToKeep = 30 } = req.body;

        // Ler dados existentes
        let analyticsData = [];
        try {
            analyticsData = await fs.readJson(ANALYTICS_FILE);
        } catch (error) {
            analyticsData = [];
        }

        // Filtrar dados recentes
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

        const recentData = analyticsData.filter(event => 
            new Date(event.timestamp) >= cutoffDate
        );

        // Salvar dados filtrados
        await fs.writeJson(ANALYTICS_FILE, recentData, { spaces: 2 });

        console.log(`🧹 Analytics cleanup: ${analyticsData.length - recentData.length} eventos removidos`);

        res.json({
            success: true,
            message: `Cleanup concluído. ${analyticsData.length - recentData.length} eventos antigos removidos.`,
            before: analyticsData.length,
            after: recentData.length
        });

    } catch (error) {
        console.error('Erro no cleanup de analytics:', error);
        res.status(500).json({
            success: false,
            message: 'Erro interno do servidor',
            error: error.message
        });
    }
});

export default router;
