import { chromium, FullConfig } from '@playwright/test';

async function globalSetup(config: FullConfig) {
  console.log('🚀 Iniciando configuração global dos testes...');
  
  // Aguardar serviços estarem prontos
  const browser = await chromium.launch();
  const page = await browser.newPage();
  
  try {
    // Verificar se o backend está rodando
    console.log('🔍 Verificando backend...');
    await page.goto('http://localhost:3002/health', { waitUntil: 'networkidle' });
    console.log('✅ Backend está rodando');
    
    // Verificar se o frontend está rodando
    console.log('🔍 Verificando frontend...');
    await page.goto('http://localhost:5173', { waitUntil: 'networkidle' });
    console.log('✅ Frontend está rodando');
    
    // Verificar se a landing page está acessível
    console.log('🔍 Verificando landing page...');
    await page.goto('http://localhost:5173/landingpage', { waitUntil: 'networkidle' });
    console.log('✅ Landing page está acessível');
    
  } catch (error) {
    console.error('❌ Erro na configuração global:', error);
    throw error;
  } finally {
    await browser.close();
  }
  
  console.log('✅ Configuração global concluída');
}

export default globalSetup;
