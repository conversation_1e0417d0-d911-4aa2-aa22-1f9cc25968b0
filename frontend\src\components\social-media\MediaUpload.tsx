import React, { useState, useRef, useCallback } from 'react';
import { Button } from '../ui/Button';
import { LoadingSpinner } from '../ui/LoadingSpinner';
import { MediaFile } from '../../types';
import { uploadMediaToFirebase, deleteMediaFromFirebase } from '../../services/mediaService';

interface MediaUploadProps {
  selectedMedia: File[];
  mediaPreviews: string[];
  onMediaChange: (files: File[], previews: string[]) => void;
  maxFiles?: number;
  acceptedTypes?: string[];
  maxFileSize?: number; // em MB
}

const MediaUpload: React.FC<MediaUploadProps> = ({
  selectedMedia,
  mediaPreviews,
  onMediaChange,
  maxFiles = 10,
  acceptedTypes = ['image/*', 'video/*'],
  maxFileSize = 50 // 50MB
}) => {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<{ [key: string]: number }>({});
  const [uploadedFiles, setUploadedFiles] = useState<MediaFile[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const validateFile = (file: File): string | null => {
    // Verificar tamanho
    if (file.size > maxFileSize * 1024 * 1024) {
      return `Arquivo muito grande. Máximo: ${maxFileSize}MB`;
    }

    // Verificar tipo
    const isValidType = acceptedTypes.some(type => {
      if (type.endsWith('/*')) {
        return file.type.startsWith(type.replace('/*', '/'));
      }
      return file.type === type;
    });

    if (!isValidType) {
      return `Tipo de arquivo não suportado. Aceitos: ${acceptedTypes.join(', ')}`;
    }

    return null;
  };

  const handleFileSelect = useCallback((files: FileList | null) => {
    if (!files) return;

    const newFiles: File[] = [];
    const newPreviews: string[] = [];
    const errors: string[] = [];

    Array.from(files).forEach(file => {
      // Verificar limite de arquivos
      if (selectedMedia.length + newFiles.length >= maxFiles) {
        errors.push(`Máximo de ${maxFiles} arquivos permitidos`);
        return;
      }

      // Validar arquivo
      const error = validateFile(file);
      if (error) {
        errors.push(`${file.name}: ${error}`);
        return;
      }

      newFiles.push(file);
      
      // Criar preview
      const preview = URL.createObjectURL(file);
      newPreviews.push(preview);
    });

    if (errors.length > 0) {
      alert(`Erros encontrados:\n${errors.join('\n')}`);
    }

    if (newFiles.length > 0) {
      onMediaChange([...selectedMedia, ...newFiles], [...mediaPreviews, ...newPreviews]);
    }
  }, [selectedMedia, mediaPreviews, maxFiles, onMediaChange]);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    handleFileSelect(e.dataTransfer.files);
  }, [handleFileSelect]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
  }, []);

  const removeFile = (index: number) => {
    const newFiles = selectedMedia.filter((_, i) => i !== index);
    const newPreviews = mediaPreviews.filter((_, i) => i !== index);
    
    // Revogar URL do preview removido
    URL.revokeObjectURL(mediaPreviews[index]);
    
    onMediaChange(newFiles, newPreviews);
  };

  const uploadToCloud = async () => {
    if (selectedMedia.length === 0) return;

    setIsUploading(true);
    const uploaded: MediaFile[] = [];

    try {
      for (let i = 0; i < selectedMedia.length; i++) {
        const file = selectedMedia[i];
        
        // Atualizar progresso
        setUploadProgress(prev => ({ ...prev, [file.name]: 0 }));

        try {
          const mediaFile = await uploadMediaToFirebase(file, (progress) => {
            setUploadProgress(prev => ({ ...prev, [file.name]: progress }));
          });
          
          uploaded.push(mediaFile);
          
          // Marcar como 100% concluído
          setUploadProgress(prev => ({ ...prev, [file.name]: 100 }));
        } catch (error) {
          console.error(`Erro ao fazer upload de ${file.name}:`, error);
          alert(`Erro ao fazer upload de ${file.name}`);
        }
      }

      setUploadedFiles(prev => [...prev, ...uploaded]);
      
      // Limpar arquivos locais após upload bem-sucedido
      if (uploaded.length === selectedMedia.length) {
        onMediaChange([], []);
        mediaPreviews.forEach(url => URL.revokeObjectURL(url));
      }

    } finally {
      setIsUploading(false);
      setUploadProgress({});
    }
  };

  const deleteUploadedFile = async (mediaFile: MediaFile) => {
    try {
      await deleteMediaFromFirebase(mediaFile.id);
      setUploadedFiles(prev => prev.filter(f => f.id !== mediaFile.id));
    } catch (error) {
      console.error('Erro ao deletar arquivo:', error);
      alert('Erro ao deletar arquivo');
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (type: string) => {
    if (type.startsWith('image/')) return '🖼️';
    if (type.startsWith('video/')) return '🎥';
    return '📄';
  };

  return (
    <div className="space-y-4">
      {/* Área de Upload */}
      <div
        className="border-2 border-dashed border-gray-300 dark:border-neutral-medium rounded-lg p-6 text-center hover:border-primary transition-colors"
        onDrop={handleDrop}
        onDragOver={handleDragOver}
      >
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept={acceptedTypes.join(',')}
          onChange={(e) => handleFileSelect(e.target.files)}
          className="hidden"
        />
        
        <div className="space-y-2">
          <div className="text-4xl">📁</div>
          <div className="text-sm text-gray-600 dark:text-neutral-medium">
            Arraste arquivos aqui ou clique para selecionar
          </div>
          <div className="text-xs text-gray-500 dark:text-neutral-medium">
            Máximo: {maxFiles} arquivos, {maxFileSize}MB cada
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => fileInputRef.current?.click()}
            disabled={isUploading}
          >
            Selecionar Arquivos
          </Button>
        </div>
      </div>

      {/* Arquivos Selecionados (Locais) */}
      {selectedMedia.length > 0 && (
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <h4 className="font-medium text-sm">Arquivos Selecionados ({selectedMedia.length})</h4>
            <div className="flex gap-2">
              <Button
                variant="primary"
                size="sm"
                onClick={uploadToCloud}
                disabled={isUploading}
                isLoading={isUploading}
              >
                {isUploading ? 'Enviando...' : 'Enviar para Nuvem'}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  onMediaChange([], []);
                  mediaPreviews.forEach(url => URL.revokeObjectURL(url));
                }}
                disabled={isUploading}
              >
                Limpar Todos
              </Button>
            </div>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
            {selectedMedia.map((file, index) => (
              <div key={index} className="relative border border-gray-200 dark:border-neutral-medium rounded-lg overflow-hidden">
                {/* Preview */}
                <div className="aspect-square bg-gray-100 dark:bg-neutral-medium flex items-center justify-center">
                  {file.type.startsWith('image/') ? (
                    <img
                      src={mediaPreviews[index]}
                      alt={file.name}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="text-2xl">{getFileIcon(file.type)}</div>
                  )}
                </div>
                
                {/* Info */}
                <div className="p-2 space-y-1">
                  <div className="text-xs font-medium truncate" title={file.name}>
                    {file.name}
                  </div>
                  <div className="text-xs text-gray-500">
                    {formatFileSize(file.size)}
                  </div>
                  
                  {/* Progress Bar */}
                  {uploadProgress[file.name] !== undefined && (
                    <div className="w-full bg-gray-200 rounded-full h-1">
                      <div
                        className="bg-primary h-1 rounded-full transition-all"
                        style={{ width: `${uploadProgress[file.name]}%` }}
                      />
                    </div>
                  )}
                </div>
                
                {/* Remove Button */}
                <button
                  onClick={() => removeFile(index)}
                  disabled={isUploading}
                  className="absolute top-1 right-1 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600 disabled:opacity-50"
                >
                  ×
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Arquivos Enviados para Nuvem */}
      {uploadedFiles.length > 0 && (
        <div className="space-y-2">
          <h4 className="font-medium text-sm">Arquivos na Nuvem ({uploadedFiles.length})</h4>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
            {uploadedFiles.map((mediaFile) => (
              <div key={mediaFile.id} className="relative border border-green-200 dark:border-green-800 rounded-lg overflow-hidden">
                {/* Preview */}
                <div className="aspect-square bg-gray-100 dark:bg-neutral-medium flex items-center justify-center">
                  {mediaFile.type.startsWith('image/') ? (
                    <img
                      src={mediaFile.thumbnailUrl || mediaFile.url}
                      alt={mediaFile.name}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="text-2xl">{getFileIcon(mediaFile.type)}</div>
                  )}
                </div>
                
                {/* Info */}
                <div className="p-2 space-y-1">
                  <div className="text-xs font-medium truncate" title={mediaFile.name}>
                    {mediaFile.name}
                  </div>
                  <div className="text-xs text-gray-500">
                    {formatFileSize(mediaFile.size)}
                  </div>
                  <div className="text-xs text-green-600">
                    ✓ Enviado
                  </div>
                </div>
                
                {/* Delete Button */}
                <button
                  onClick={() => deleteUploadedFile(mediaFile)}
                  className="absolute top-1 right-1 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600"
                >
                  ×
                </button>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default MediaUpload;
