@echo off
echo ========================================
echo   INSTALANDO DEPENDENCIAS DE TESTE - E2E
echo ========================================
echo.

echo [1/4] Instalando dependencias do projeto raiz...
npm install --save-dev @playwright/test@^1.49.1 typescript@^5.7.2

if %errorlevel% neq 0 (
    echo ERRO: Falha ao instalar dependencias do Playwright
    pause
    exit /b 1
)

echo.
echo [2/4] Verificando instalacao do Playwright...
npx playwright --version

if %errorlevel% neq 0 (
    echo ERRO: Playwright nao foi instalado corretamente
    pause
    exit /b 1
)

echo.
echo [3/4] Instalando browsers do Playwright...
npx playwright install

if %errorlevel% neq 0 (
    echo ERRO: Falha ao instalar browsers do Playwright
    pause
    exit /b 1
)

echo.
echo [4/4] Verificando browsers instalados...
npx playwright install --dry-run

echo.
echo ========================================
echo   PLAYWRIGHT INSTALADO COM SUCESSO!
echo ========================================
echo.
echo Agora voce pode executar:
echo - npm run test:e2e
echo - npm run test:e2e:ui
echo - npm run test:e2e:headed
echo.

pause
