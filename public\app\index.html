<!DOCTYPE html>
<html lang="pt-BR">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Pro-Mandato</title>

    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        darkMode: 'class', // Enable class-based dark mode
        theme: {
          extend: {
            colors: {
              primary: {
                light: '#60a5fa', // blue-400
                DEFAULT: '#3b82f6', // blue-500
                dark: '#2563eb', // blue-600
              },
              secondary: {
                light: '#fde047', // yellow-300
                DEFAULT: '#facc15', // yellow-400
                dark: '#eab308', // yellow-500
              },
              neutral: {
                extralight: '#f9fafb', // gray-50 (for dark text on dark backgrounds if needed, or light text on light)
                light: '#f3f4f6',    // gray-100
                DEFAULT: '#9ca3af',  // gray-400
                medium: '#6b7280',   // gray-500 (added for intermediate text)
                dark: '#374151',     // gray-700
                darker: '#1f2937',   // gray-800 (added for dark mode elements)
                darkest: '#111827',  // gray-900 (added for dark mode backgrounds)
              }
            }
          }
        }
      }
    </script>
    <script type="module" crossorigin src="/assets/index-CaAmtzid.js"></script>
    <link rel="stylesheet" crossorigin href="/assets/index-Bay0qCbN.css">
  </head>
  <body class="bg-neutral-light text-neutral-dark dark:bg-neutral-darkest dark:text-neutral-light transition-colors duration-300">
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>

  </body>
</html>