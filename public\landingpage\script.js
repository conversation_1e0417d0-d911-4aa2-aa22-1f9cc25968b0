// SCRIPT VERSION CHECK: 2025-06-21 11:00 AM
console.log('PRO MANDATO LANDING PAGE SCRIPT - VERSION CHECK: 2025-06-21 11:00 AM -- Se você não vir esta mensagem, o script antigo está em cache!');

/**
 * Script principal da Landing Page Promandato
 * Funcionalidades interativas e animações
 */

// Configurações globais
const CONFIG = {
    // Preços dos planos (valores reais do backend)
    pricing: {
        monthly: {
            basic: 169.90,
            standard: 259.90,
            professional: 599.90  // ✅ Corrigido para valor real
        },
        yearly: {
            basic: 1834.92 / 12, // Valor anual dividido por 12
            standard: 2807.08 / 12,
            professional: 6478.92 / 12  // ✅ Corrigido para valor real
        }
    },
    
    // Configurações de animação
    animation: {
        duration: 300,
        easing: 'ease-in-out'
    },
    
    // Configurações do observer
    observer: {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    }
};

// Estado da aplicação
const state = {
    isYearlyBilling: false,
    isMobileMenuOpen: false,
    activeFAQ: null
};

/**
 * Inicialização da aplicação
 */
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

async function initializeApp() {
    // Inicializar ícones Lucide
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }

    // Configurar event listeners
    setupEventListeners();

    // Configurar animações de scroll
    setupScrollAnimations();

    // Configurar smooth scrolling
    setupSmoothScrolling();

    // Carregar dados dinâmicos dos planos
    await loadDynamicPlans();

    console.log('Landing Page Promandato inicializada com sucesso!');
}

/**
 * Carregar planos dinamicamente da API
 */
async function loadDynamicPlans() {
    try {
        console.log('Carregando planos da API...');

        if (window.plansManager) {
            await window.plansManager.loadPlans();
            console.log('Planos carregados com sucesso');
        } else {
            console.warn('PlansManager não encontrado, usando dados estáticos');
        }
    } catch (error) {
        console.error('Erro ao carregar planos:', error);
        // Continuar com dados estáticos em caso de erro
    }
}

/**
 * Configurar todos os event listeners
 */
function setupEventListeners() {
    // Menu mobile
    setupMobileMenu();

    // Toggle de preços
    setupPricingToggle();

    // FAQ accordion
    setupFAQAccordion();

    // Formulários (se existirem)
    setupForms();
}

/**
 * Scroll para seção específica
 */
function scrollToSection(sectionId) {
    const element = document.getElementById(sectionId);
    if (element) {
        const headerHeight = 80;
        const targetPosition = element.offsetTop - headerHeight;

        window.scrollTo({
            top: targetPosition,
            behavior: 'smooth'
        });
    }
}

/**
 * Solicitar demonstração
 */
function requestDemo() {
    // Scroll para o formulário de contato
    scrollToSection('contact');

    // Focar no campo de nome
    setTimeout(() => {
        const nameField = document.getElementById('name');
        if (nameField) {
            nameField.focus();
        }
    }, 500);
}

/**
 * Redirecionar para o aplicativo
 */
function redirectToApp() {
    // Usar configuração do ambiente
    const appUrl = window.LANDING_CONFIG?.APP_URL || 'https://app.promandato.com.br';

    // Abrir em nova aba
    window.open(appUrl, '_blank');
}

/**
 * Selecionar plano (integração com Stripe)
 */
function selectPlan(planType) {
    if (window.StripeIntegration) {
        const billingCycle = state.isYearlyBilling ? 'yearly' : 'monthly';
        window.StripeIntegration.createCheckoutSession(planType, billingCycle);
    } else {
        console.error('Stripe integration not loaded');
        // Fallback: scroll para seção de contato
        scrollToSection('contact');
    }
}

/**
 * Toggle FAQ
 */
function toggleFAQ(faqNumber) {
    const content = document.getElementById(`faq-content-${faqNumber}`);
    const icon = document.getElementById(`faq-icon-${faqNumber}`);

    if (content && icon) {
        const isHidden = content.classList.contains('hidden');

        // Fechar todas as outras FAQs
        document.querySelectorAll('[id^="faq-content-"]').forEach(el => {
            el.classList.add('hidden');
        });
        document.querySelectorAll('[id^="faq-icon-"]').forEach(el => {
            el.classList.remove('rotate-180');
        });

        if (isHidden) {
            content.classList.remove('hidden');
            icon.classList.add('rotate-180');
            state.activeFAQ = faqNumber;
        } else {
            state.activeFAQ = null;
        }
    }
}

/**
 * Menu mobile
 */
function setupMobileMenu() {
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const mobileMenu = document.getElementById('mobile-menu');

    if (mobileMenuButton && mobileMenu) {
        mobileMenuButton.addEventListener('click', toggleMobileMenu);

        // Fechar menu ao clicar em links
        const menuLinks = mobileMenu.querySelectorAll('a');
        menuLinks.forEach(link => {
            link.addEventListener('click', closeMobileMenu);
        });

        // Fechar menu ao clicar fora
        document.addEventListener('click', function(e) {
            if (!mobileMenuButton.contains(e.target) && !mobileMenu.contains(e.target)) {
                closeMobileMenu();
            }
        });
    }
}

function toggleMobileMenu() {
    const mobileMenu = document.getElementById('mobile-menu');
    const menuIcon = document.querySelector('#mobile-menu-button i');

    state.isMobileMenuOpen = !state.isMobileMenuOpen;

    if (state.isMobileMenuOpen) {
        mobileMenu.classList.remove('hidden');
        menuIcon.setAttribute('data-lucide', 'x');
    } else {
        mobileMenu.classList.add('hidden');
        menuIcon.setAttribute('data-lucide', 'menu');
    }

    // Reinicializar ícones
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
}

function closeMobileMenu() {
    const mobileMenu = document.getElementById('mobile-menu');
    const menuIcon = document.querySelector('#mobile-menu-button i');

    state.isMobileMenuOpen = false;
    if (mobileMenu) mobileMenu.classList.add('hidden');
    if (menuIcon) menuIcon.setAttribute('data-lucide', 'menu');

    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
}

/**
 * Toggle de preços (mensal/anual)
 */
function setupPricingToggle() {
    const monthlyBtn = document.getElementById('monthly-btn');
    const yearlyBtn = document.getElementById('yearly-btn');

    if (monthlyBtn && yearlyBtn) {
        monthlyBtn.addEventListener('click', () => setPricingMode('monthly'));
        yearlyBtn.addEventListener('click', () => setPricingMode('yearly'));
    }
}

function setPricingMode(mode) {
    state.isYearlyBilling = mode === 'yearly';

    const monthlyBtn = document.getElementById('monthly-btn');
    const yearlyBtn = document.getElementById('yearly-btn');

    if (monthlyBtn && yearlyBtn) {
        if (mode === 'monthly') {
            monthlyBtn.classList.add('bg-primary-600', 'text-white');
            monthlyBtn.classList.remove('text-gray-600');
            yearlyBtn.classList.remove('bg-primary-600', 'text-white');
            yearlyBtn.classList.add('text-gray-600');
        } else {
            yearlyBtn.classList.add('bg-primary-600', 'text-white');
            yearlyBtn.classList.remove('text-gray-600');
            monthlyBtn.classList.remove('bg-primary-600', 'text-white');
            monthlyBtn.classList.add('text-gray-600');
        }
    }

    updatePricing();
}

function updatePricing() {
    // Usar dados dinâmicos se disponíveis, senão usar fallback
    if (window.plansManager && window.plansManager.plans.length > 0) {
        window.plansManager.updatePricingDisplay();
    } else {
        // Fallback para dados estáticos
        const pricing = CONFIG.pricing;
        const mode = state.isYearlyBilling ? 'yearly' : 'monthly';

        // Atualizar preços nos cards
        const basicPrice = document.getElementById('basic-price');
        const standardPrice = document.getElementById('standard-price');
        const professionalPrice = document.getElementById('professional-price');

        if (basicPrice) basicPrice.textContent = formatPrice(pricing[mode].basic);
        if (standardPrice) standardPrice.textContent = formatPrice(pricing[mode].standard);
        if (professionalPrice) professionalPrice.textContent = formatPrice(pricing[mode].professional);

        // Mostrar/esconder informações de economia anual
        const savingsElements = document.querySelectorAll('[id$="-yearly-savings"]');
        savingsElements.forEach(el => {
            if (state.isYearlyBilling) {
                el.style.display = 'block';
            } else {
                el.style.display = 'none';
            }
        });
    }
}

/**
 * Formatar preço
 */
function formatPrice(price) {
    return new Intl.NumberFormat('pt-BR', {
        style: 'currency',
        currency: 'BRL'
    }).format(price);
}

/**
 * FAQ Accordion
 */
function setupFAQAccordion() {
    const faqButtons = document.querySelectorAll('.faq-button');
    
    faqButtons.forEach((button, index) => {
        button.addEventListener('click', () => toggleFAQ(index));
    });
}

function toggleFAQ(index) {
    const faqButtons = document.querySelectorAll('.faq-button');
    const faqContents = document.querySelectorAll('.faq-content');
    
    // Fechar todas as outras FAQs
    faqButtons.forEach((button, i) => {
        if (i !== index) {
            const content = faqContents[i];
            const icon = button.querySelector('i');
            
            content.classList.add('hidden');
            icon.classList.remove('rotate-180');
        }
    });
    
    // Toggle da FAQ atual
    const currentContent = faqContents[index];
    const currentIcon = faqButtons[index].querySelector('i');
    
    if (state.activeFAQ === index) {
        // Fechar FAQ atual
        currentContent.classList.add('hidden');
        currentIcon.classList.remove('rotate-180');
        state.activeFAQ = null;
    } else {
        // Abrir FAQ atual
        currentContent.classList.remove('hidden');
        currentIcon.classList.add('rotate-180');
        state.activeFAQ = index;
    }
}

/**
 * Smooth scrolling para links âncora
 */
function setupSmoothScrolling() {
    const anchorLinks = document.querySelectorAll('a[href^="#"]');
    
    anchorLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href');
            const targetElement = document.querySelector(targetId);
            
            if (targetElement) {
                const headerHeight = 80; // Altura do header fixo
                const targetPosition = targetElement.offsetTop - headerHeight;
                
                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
                
                // Fechar menu mobile se estiver aberto
                closeMobileMenu();
            }
        });
    });
}

/**
 * Animações de scroll
 */
function setupScrollAnimations() {
    const observerOptions = {
        threshold: CONFIG.observer.threshold,
        rootMargin: CONFIG.observer.rootMargin
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-fade-in');
                
                // Adicionar delay para elementos filhos
                const children = entry.target.querySelectorAll('.animate-on-scroll');
                children.forEach((child, index) => {
                    setTimeout(() => {
                        child.classList.add('animate-fade-in');
                    }, index * 100);
                });
            }
        });
    }, observerOptions);
    
    // Observar seções
    const sections = document.querySelectorAll('section');
    sections.forEach(section => {
        observer.observe(section);
    });
    
    // Observar cards e elementos especiais
    const animatedElements = document.querySelectorAll('.card-hover, .testimonial-card');
    animatedElements.forEach(element => {
        observer.observe(element);
    });
}

/**
 * Configurar formulários
 */
function setupForms() {
    const forms = document.querySelectorAll('form');
    
    forms.forEach(form => {
        form.addEventListener('submit', handleFormSubmit);
    });
}

async function handleFormSubmit(e) {
    e.preventDefault();

    const form = e.target;
    const formData = new FormData(form);
    const data = Object.fromEntries(formData);

    // Mostrar indicador de carregamento
    const submitButton = form.querySelector('button[type="submit"]');
    const originalText = submitButton.textContent;
    submitButton.disabled = true;
    submitButton.textContent = 'Enviando...';

    try {
        // Determinar se é formulário de contato ou demo
        const isContactForm = form.id === 'contact-form';

        let response;
        if (window.apiService) {
            if (isContactForm) {
                response = await window.apiService.submitContactForm(data);
            } else {
                response = await window.apiService.requestDemo(data);
            }
        } else {
            // Fallback para envio direto
            const endpoint = isContactForm ? '/api/public/contact' : '/api/public/demo';
            response = await fetch(`http://localhost:3002${endpoint}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            });
            response = await response.json();
        }

        if (response.success) {
            showNotification(response.message || 'Formulário enviado com sucesso!', 'success');
            form.reset();
        } else {
            throw new Error(response.error || 'Erro ao enviar formulário');
        }
    } catch (error) {
        console.error('Erro ao enviar formulário:', error);
        showNotification('Erro ao enviar formulário. Tente novamente.', 'error');
    } finally {
        // Restaurar botão
        submitButton.disabled = false;
        submitButton.textContent = originalText;
    }
}

/**
 * Sistema de notificações
 */
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 ${getNotificationClasses(type)}`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    // Remover após 3 segundos
    setTimeout(() => {
        notification.remove();
    }, 3000);
}

function getNotificationClasses(type) {
    const classes = {
        success: 'bg-green-500 text-white',
        error: 'bg-red-500 text-white',
        warning: 'bg-yellow-500 text-black',
        info: 'bg-blue-500 text-white'
    };
    
    return classes[type] || classes.info;
}

/**
 * Utilitários
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

/**
 * Analytics e tracking (placeholder)
 */
function trackEvent(eventName, properties = {}) {
    // Implementar tracking com Google Analytics, Facebook Pixel, etc.
    console.log('Event tracked:', eventName, properties);
    
    // Exemplo para Google Analytics
    if (typeof gtag !== 'undefined') {
        gtag('event', eventName, properties);
    }
}

/**
 * Toggle de billing (função global)
 */
// Define toggleBilling diretamente no objeto window
window.toggleBilling = function() {
    // Garante que setPricingMode está disponível.
    // Idealmente, setPricingMode também deveria estar no window ou bem escopada.
    if (typeof setPricingMode === 'function') {
        setPricingMode(state.isYearlyBilling ? 'monthly' : 'yearly');
    } else if (typeof window.setPricingMode === 'function') {
        window.setPricingMode(state.isYearlyBilling ? 'monthly' : 'yearly');
    } else {
        console.error('FATAL: setPricingMode não está definida dentro de toggleBilling');
    }
};

// Exportar funções para uso global se necessário

// Garante que PromandatoLP exista ou o inicializa
window.PromandatoLP = window.PromandatoLP || {};

// Atribui funções ao PromandatoLP e garante que estejam no window,
// caso ainda não estejam por atribuição direta.

// Para toggleBilling, já está no window. Agora atribui ao PromandatoLP.
if (typeof window.toggleBilling === 'function') {
    window.PromandatoLP.toggleBilling = window.toggleBilling;
} else {
    // Este caso idealmente não deveria ser atingido se a atribuição acima funcionou.
    console.error('FATAL: window.toggleBilling não foi definida com sucesso como uma função.');
}

// Trata outras funções de forma similar, garantindo que sejam definidas antes do uso.
// Assegura que as funções globais declaradas (ex: function trackEvent() {...}) sejam expostas.

const functionsToExport = {
    trackEvent,
    showNotification,
    toggleMobileMenu,
    setPricingMode, // setPricingMode é chamada por window.toggleBilling
    scrollToSection,
    requestDemo,
    selectPlan, // Para onclicks no HTML
    toggleFAQ   // Para o accordion de FAQ no HTML
};

for (const funcName in functionsToExport) {
    if (typeof functionsToExport[funcName] === 'function') {
        if (!window[funcName]) { // Se ainda não estiver no window (como toggleBilling já está)
            window[funcName] = functionsToExport[funcName];
        }
        if (window.PromandatoLP && !window.PromandatoLP[funcName]) { // Adiciona ao PromandatoLP se não estiver lá
             window.PromandatoLP[funcName] = window[funcName];
        }
    } else {
        console.warn(`A função ${funcName} não está definida globalmente para exportação.`);
    }
}

// Garante que toggleFAQ (para o accordion) esteja no window se tiver o nome esperado,
// para evitar conflitos caso haja outra função toggleFAQ.
if (typeof toggleFAQ === 'function' && toggleFAQ.name === 'toggleFAQ') {
    window.toggleFAQ = toggleFAQ;
} else if (typeof window.toggleFAQ !== 'function' && typeof toggleFAQ === 'function') {
    // Se window.toggleFAQ não foi setado pelo loop acima mas toggleFAQ existe
    console.warn("Atribuindo toggleFAQ ao window (verificação de nome falhou ou toggleFAQ não estava no loop inicial).");
    window.toggleFAQ = toggleFAQ;
}
