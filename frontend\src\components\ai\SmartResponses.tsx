import React, { useState } from 'react';
import { ICONS } from '../../constants';
import { usePlan } from '../../hooks/usePlan';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';
import { Textarea } from '../ui/Textarea';

interface ResponseSuggestion {
  id: string;
  text: string;
  tone: 'formal' | 'friendly' | 'empathetic' | 'professional';
  category: string;
  confidence: number;
  estimatedTime: string;
}

interface SmartResponseResult {
  originalDemand: string;
  category: string;
  urgency: 'low' | 'medium' | 'high';
  suggestions: ResponseSuggestion[];
  keyPoints: string[];
  recommendedTone: string;
}

const SmartResponses: React.FC = () => {
  const { canUseAIFeature, getAIFeatureUsage, incrementAIUsage } = usePlan();
  const [demandText, setDemandText] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [result, setResult] = useState<SmartResponseResult | null>(null);
  const [selectedSuggestion, setSelectedSuggestion] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  const usage = getAIFeatureUsage('smart-responses');

  const handleGenerateResponses = async () => {
    if (!demandText.trim()) return;

    setIsGenerating(true);
    setError(null);

    try {
      const canProceed = await incrementAIUsage('smart-responses');
      if (!canProceed) {
        setError('Limite de uso atingido para este mês');
        return;
      }

      // Simular geração de respostas inteligentes
      await new Promise(resolve => setTimeout(resolve, 2500));

      // Análise simples para determinar categoria e urgência
      const text = demandText.toLowerCase();
      let category = 'Geral';
      let urgency: 'low' | 'medium' | 'high' = 'medium';

      if (text.includes('saúde') || text.includes('hospital') || text.includes('médico')) {
        category = 'Saúde';
        urgency = 'high';
      } else if (text.includes('educação') || text.includes('escola') || text.includes('ensino')) {
        category = 'Educação';
        urgency = 'medium';
      } else if (text.includes('infraestrutura') || text.includes('rua') || text.includes('asfalto')) {
        category = 'Infraestrutura';
        urgency = 'medium';
      } else if (text.includes('urgente') || text.includes('emergência')) {
        urgency = 'high';
      }

      const mockResult: SmartResponseResult = {
        originalDemand: demandText,
        category,
        urgency,
        suggestions: [
          {
            id: '1',
            text: `Prezado(a) cidadão(ã),\n\nRecebemos sua solicitação relacionada a ${category.toLowerCase()} e informamos que ela foi registrada em nosso sistema com o protocolo #${Math.floor(Math.random() * 10000)}.\n\nNossa equipe técnica irá analisar o caso e retornaremos com uma posição em até 5 dias úteis.\n\nAtenciosamente,\nEquipe de Atendimento`,
            tone: 'formal',
            category: 'Confirmação de Recebimento',
            confidence: 0.92,
            estimatedTime: '2 min'
          },
          {
            id: '2',
            text: `Olá!\n\nObrigado por entrar em contato conosco. Sua demanda sobre ${category.toLowerCase()} é muito importante para nós.\n\nJá encaminhamos sua solicitação para o setor responsável e você receberá uma resposta em breve. Enquanto isso, se tiver alguma dúvida, não hesite em nos procurar.\n\nUm abraço,\nEquipe de Atendimento`,
            tone: 'friendly',
            category: 'Resposta Amigável',
            confidence: 0.88,
            estimatedTime: '1 min'
          },
          {
            id: '3',
            text: `Caro(a) cidadão(ã),\n\nCompreendemos sua preocupação e queremos assegurar que sua solicitação receberá toda a atenção necessária.\n\nNossos especialistas em ${category.toLowerCase()} já foram notificados e estão trabalhando para encontrar a melhor solução para seu caso.\n\nManteremos você informado sobre o andamento do processo.\n\nCordialmente,\nGestão Pública`,
            tone: 'empathetic',
            category: 'Resposta Empática',
            confidence: 0.85,
            estimatedTime: '3 min'
          },
          {
            id: '4',
            text: `Protocolo: #${Math.floor(Math.random() * 10000)}\nData: ${new Date().toLocaleDateString('pt-BR')}\n\nSua demanda foi classificada como "${category}" e está sendo processada conforme nossos procedimentos padrão.\n\nPrazo estimado para resposta: ${urgency === 'high' ? '24-48h' : '3-5 dias úteis'}\nSetor responsável: Departamento de ${category}\n\nAcompanhe o status pelo portal do cidadão.\n\nAtendimento Público`,
            tone: 'professional',
            category: 'Resposta Técnica',
            confidence: 0.90,
            estimatedTime: '1 min'
          }
        ],
        keyPoints: [
          'Confirmar recebimento da demanda',
          'Informar protocolo de atendimento',
          'Estabelecer prazo de resposta',
          'Demonstrar que a solicitação está sendo tratada',
          'Manter canal de comunicação aberto'
        ],
        recommendedTone: urgency === 'high' ? 'empathetic' : 'professional'
      };

      setResult(mockResult);
    } catch (error) {
      console.error('Erro na geração de respostas:', error);
      setError('Erro ao gerar respostas inteligentes. Tente novamente.');
    } finally {
      setIsGenerating(false);
    }
  };

  const getToneIcon = (tone: string) => {
    switch (tone) {
      case 'formal':
        return <div className="w-4 h-4 text-blue-500">{ICONS.FILE_TEXT}</div>;
      case 'friendly':
        return <div className="w-4 h-4 text-green-500">{ICONS.HEART}</div>;
      case 'empathetic':
        return <div className="w-4 h-4 text-purple-500">{ICONS.USERS}</div>;
      case 'professional':
        return <div className="w-4 h-4 text-gray-600">{ICONS.BRIEFCASE}</div>;
      default:
        return <div className="w-4 h-4 text-gray-500">{ICONS.MESSAGE_SQUARE}</div>;
    }
  };

  const getToneColor = (tone: string) => {
    switch (tone) {
      case 'formal':
        return 'bg-blue-50 border-blue-200 text-blue-700';
      case 'friendly':
        return 'bg-green-50 border-green-200 text-green-700';
      case 'empathetic':
        return 'bg-purple-50 border-purple-200 text-purple-700';
      case 'professional':
        return 'bg-gray-50 border-gray-200 text-gray-700';
      default:
        return 'bg-gray-50 border-gray-200 text-gray-700';
    }
  };

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'high':
        return 'bg-red-50 border-red-200 text-red-700';
      case 'medium':
        return 'bg-yellow-50 border-yellow-200 text-yellow-700';
      case 'low':
        return 'bg-green-50 border-green-200 text-green-700';
      default:
        return 'bg-gray-50 border-gray-200 text-gray-700';
    }
  };

  const getUrgencyLabel = (urgency: string) => {
    switch (urgency) {
      case 'high':
        return 'Alta Urgência';
      case 'medium':
        return 'Média Urgência';
      case 'low':
        return 'Baixa Urgência';
      default:
        return 'Urgência Indefinida';
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    // Aqui você poderia adicionar uma notificação de sucesso
  };

  if (!canUseAIFeature('smart-responses')) {
    return (
      <Card className="p-6">
        <div className="text-center py-8">
          <div className="bg-orange-100 rounded-full p-4 inline-block mb-4">
            <div className="w-8 h-8 text-orange-600">
              {ICONS.LOCK}
            </div>
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            Respostas Inteligentes Indisponíveis
          </h3>
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
            <p className="text-sm text-blue-700">
              Faça upgrade para o plano Profissional para usar respostas inteligentes com IA.
            </p>
          </div>
          <Button variant="primary">
            Ver Planos
          </Button>
        </div>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card className="p-6 shadow-lg border-0 bg-gradient-to-br from-orange-50 to-amber-50">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 text-orange-600 bg-white rounded-xl p-2 shadow-md">
              {ICONS.MESSAGE_SQUARE}
            </div>
            <div>
              <h2 className="text-2xl font-bold bg-gradient-to-r from-orange-600 to-amber-600 bg-clip-text text-transparent">
                Respostas Inteligentes
              </h2>
              <p className="text-sm text-gray-600">Sugestões automáticas de respostas para demandas</p>
            </div>
          </div>
          {usage && usage.limit > 0 && (
            <div className="bg-white/70 backdrop-blur-sm rounded-full px-4 py-2 border border-white/20">
              <div className="text-sm text-gray-700 font-medium">
                {usage.used} / {usage.limit} gerações este mês
              </div>
            </div>
          )}
        </div>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Demanda do Cidadão
            </label>
            <Textarea
              value={demandText}
              onChange={(e) => setDemandText(e.target.value)}
              placeholder="Cole aqui a demanda ou solicitação do cidadão para gerar respostas inteligentes..."
              rows={4}
              className="w-full"
            />
          </div>

          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          )}

          <Button
            onClick={handleGenerateResponses}
            disabled={!demandText.trim() || isGenerating}
            isLoading={isGenerating}
            loadingText="Gerando respostas..."
            className="w-full bg-gradient-to-r from-orange-500 to-amber-500 hover:from-orange-600 hover:to-amber-600 text-white font-semibold py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
          >
            <div className="w-5 h-5 mr-2">
              {ICONS.BRAIN}
            </div>
            Gerar Respostas Inteligentes
          </Button>
        </div>
      </Card>

      {result && (
        <>
          {/* Análise da Demanda */}
          <Card className="p-6 shadow-lg border-0 bg-gradient-to-br from-blue-50 to-indigo-50">
            <div className="flex items-center space-x-2 mb-6">
              <div className="w-6 h-6 text-blue-600">
                {ICONS.BRAIN}
              </div>
              <h3 className="text-xl font-semibold text-gray-900">Análise da Demanda</h3>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                <div className="text-sm font-medium text-blue-900 mb-1">Categoria</div>
                <div className="text-blue-700">{result.category}</div>
              </div>
              <div className={`border rounded-lg p-3 ${getUrgencyColor(result.urgency)}`}>
                <div className="text-sm font-medium mb-1">Urgência</div>
                <div>{getUrgencyLabel(result.urgency)}</div>
              </div>
              <div className="bg-gray-50 border border-gray-200 rounded-lg p-3">
                <div className="text-sm font-medium text-gray-900 mb-1">Tom Recomendado</div>
                <div className="text-gray-700 capitalize">{result.recommendedTone}</div>
              </div>
            </div>

            {result.keyPoints.length > 0 && (
              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-2">Pontos-chave para a resposta:</h4>
                <ul className="space-y-1">
                  {result.keyPoints.map((point, index) => (
                    <li key={index} className="text-sm text-gray-600 flex items-start">
                      <span className="text-orange-500 mr-2">•</span>
                      {point}
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </Card>

          {/* Sugestões de Resposta */}
          <Card className="p-6 shadow-lg border-0 bg-white">
            <div className="flex items-center space-x-2 mb-6">
              <div className="w-6 h-6 text-green-600">
                {ICONS.MESSAGE_SQUARE}
              </div>
              <h3 className="text-xl font-semibold text-gray-900">Sugestões de Resposta</h3>
            </div>
            
            <div className="space-y-4">
              {result.suggestions.map((suggestion) => (
                <div 
                  key={suggestion.id} 
                  className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                    selectedSuggestion === suggestion.id 
                      ? 'border-orange-300 bg-orange-50' 
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => setSelectedSuggestion(
                    selectedSuggestion === suggestion.id ? null : suggestion.id
                  )}
                >
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center space-x-2">
                      {getToneIcon(suggestion.tone)}
                      <span className="font-medium text-gray-900">{suggestion.category}</span>
                      <span className={`px-2 py-1 rounded text-xs border ${getToneColor(suggestion.tone)}`}>
                        {suggestion.tone}
                      </span>
                    </div>
                    <div className="flex items-center space-x-2 text-sm text-gray-500">
                      <span>Confiança: {(suggestion.confidence * 100).toFixed(0)}%</span>
                      <span>•</span>
                      <span>{suggestion.estimatedTime}</span>
                    </div>
                  </div>

                  <div className="bg-white rounded border p-3 mb-3">
                    <pre className="whitespace-pre-wrap text-sm text-gray-700 font-sans">
                      {suggestion.text}
                    </pre>
                  </div>

                  <div className="flex justify-end space-x-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={(e) => {
                        e.stopPropagation();
                        copyToClipboard(suggestion.text);
                      }}
                    >
                      <div className="w-4 h-4 mr-1">
                        {ICONS.COPY}
                      </div>
                      Copiar
                    </Button>
                    <Button
                      size="sm"
                      variant="primary"
                      onClick={(e) => {
                        e.stopPropagation();
                        // Aqui você implementaria a funcionalidade de usar a resposta
                      }}
                    >
                      <div className="w-4 h-4 mr-1">
                        {ICONS.CHECK}
                      </div>
                      Usar Resposta
                    </Button>
                  </div>
                </div>
              ))}
            </div>

            <div className="mt-6 bg-orange-50 border border-orange-200 rounded-lg p-4">
              <h4 className="text-sm font-medium text-orange-900 mb-2">🤖 IA Profissional</h4>
              <p className="text-sm text-orange-700">
                Estas respostas foram geradas automaticamente pela IA com base no conteúdo da demanda. 
                Revise e personalize conforme necessário antes de enviar ao cidadão.
              </p>
            </div>
          </Card>
        </>
      )}
    </div>
  );
};

export default SmartResponses;
