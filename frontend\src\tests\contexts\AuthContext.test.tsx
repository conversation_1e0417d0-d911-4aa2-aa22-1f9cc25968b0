import { render, screen, waitFor } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { AuthProvider, useAuth } from '../../contexts/AuthContext'
import { ReactNode } from 'react'

// Componente de teste para usar o contexto
const TestComponent = () => {
  const { user, loading, login, logout, register } = useAuth()
  
  return (
    <div>
      <div data-testid="loading">{loading ? 'loading' : 'not-loading'}</div>
      <div data-testid="user">{user ? user.email : 'no-user'}</div>
      <button onClick={() => login('<EMAIL>', 'password')}>Login</button>
      <button onClick={() => register('<EMAIL>', 'password', 'New User')}>Register</button>
      <button onClick={logout}>Logout</button>
    </div>
  )
}

const renderWithAuthProvider = (children: ReactNode) => {
  return render(
    <AuthProvider>
      {children}
    </AuthProvider>
  )
}

describe('AuthContext', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    localStorage.clear()
  })

  it('provides initial state correctly', () => {
    renderWithAuthProvider(<TestComponent />)
    
    expect(screen.getByTestId('loading')).toHaveTextContent('not-loading')
    expect(screen.getByTestId('user')).toHaveTextContent('no-user')
  })

  it('shows loading state during authentication', async () => {
    // Mock fetch para simular login
    global.fetch = vi.fn(() =>
      Promise.resolve({
        ok: true,
        json: () => Promise.resolve({
          success: true,
          user: { id: '1', email: '<EMAIL>', name: 'Test User' },
          token: 'fake-token'
        })
      })
    ) as any

    renderWithAuthProvider(<TestComponent />)
    
    const loginButton = screen.getByText('Login')
    loginButton.click()
    
    // Deve mostrar loading temporariamente
    expect(screen.getByTestId('loading')).toHaveTextContent('loading')
    
    // Aguardar o login completar
    await waitFor(() => {
      expect(screen.getByTestId('loading')).toHaveTextContent('not-loading')
    })
  })

  it('handles successful login', async () => {
    const mockUser = { id: '1', email: '<EMAIL>', name: 'Test User' }
    const mockToken = 'fake-token'

    global.fetch = vi.fn(() =>
      Promise.resolve({
        ok: true,
        json: () => Promise.resolve({
          success: true,
          user: mockUser,
          token: mockToken
        })
      })
    ) as any

    renderWithAuthProvider(<TestComponent />)
    
    const loginButton = screen.getByText('Login')
    loginButton.click()
    
    await waitFor(() => {
      expect(screen.getByTestId('user')).toHaveTextContent('<EMAIL>')
    })

    // Verificar se o token foi salvo no localStorage
    expect(localStorage.setItem).toHaveBeenCalledWith('token', mockToken)
  })

  it('handles login failure', async () => {
    global.fetch = vi.fn(() =>
      Promise.resolve({
        ok: false,
        json: () => Promise.resolve({
          success: false,
          message: 'Invalid credentials'
        })
      })
    ) as any

    renderWithAuthProvider(<TestComponent />)
    
    const loginButton = screen.getByText('Login')
    loginButton.click()
    
    await waitFor(() => {
      expect(screen.getByTestId('user')).toHaveTextContent('no-user')
      expect(screen.getByTestId('loading')).toHaveTextContent('not-loading')
    })
  })

  it('handles successful registration', async () => {
    const mockUser = { id: '2', email: '<EMAIL>', name: 'New User' }
    const mockToken = 'new-fake-token'

    global.fetch = vi.fn(() =>
      Promise.resolve({
        ok: true,
        json: () => Promise.resolve({
          success: true,
          user: mockUser,
          token: mockToken
        })
      })
    ) as any

    renderWithAuthProvider(<TestComponent />)
    
    const registerButton = screen.getByText('Register')
    registerButton.click()
    
    await waitFor(() => {
      expect(screen.getByTestId('user')).toHaveTextContent('<EMAIL>')
    })

    expect(localStorage.setItem).toHaveBeenCalledWith('token', mockToken)
  })

  it('handles logout correctly', async () => {
    // Primeiro fazer login
    const mockUser = { id: '1', email: '<EMAIL>', name: 'Test User' }
    const mockToken = 'fake-token'

    global.fetch = vi.fn(() =>
      Promise.resolve({
        ok: true,
        json: () => Promise.resolve({
          success: true,
          user: mockUser,
          token: mockToken
        })
      })
    ) as any

    renderWithAuthProvider(<TestComponent />)
    
    const loginButton = screen.getByText('Login')
    loginButton.click()
    
    await waitFor(() => {
      expect(screen.getByTestId('user')).toHaveTextContent('<EMAIL>')
    })

    // Agora fazer logout
    const logoutButton = screen.getByText('Logout')
    logoutButton.click()
    
    await waitFor(() => {
      expect(screen.getByTestId('user')).toHaveTextContent('no-user')
    })

    // Verificar se o token foi removido do localStorage
    expect(localStorage.removeItem).toHaveBeenCalledWith('token')
  })

  it('restores user from localStorage on mount', async () => {
    const mockToken = 'stored-token'
    const mockUser = { id: '1', email: '<EMAIL>', name: 'Stored User' }

    // Simular token no localStorage
    localStorage.getItem = vi.fn((key) => {
      if (key === 'token') return mockToken
      return null
    })

    // Mock da verificação do token
    global.fetch = vi.fn(() =>
      Promise.resolve({
        ok: true,
        json: () => Promise.resolve({
          success: true,
          user: mockUser
        })
      })
    ) as any

    renderWithAuthProvider(<TestComponent />)
    
    await waitFor(() => {
      expect(screen.getByTestId('user')).toHaveTextContent('<EMAIL>')
    })
  })

  it('handles invalid stored token', async () => {
    const mockToken = 'invalid-token'

    localStorage.getItem = vi.fn((key) => {
      if (key === 'token') return mockToken
      return null
    })

    // Mock da verificação do token falhando
    global.fetch = vi.fn(() =>
      Promise.resolve({
        ok: false,
        json: () => Promise.resolve({
          success: false,
          message: 'Invalid token'
        })
      })
    ) as any

    renderWithAuthProvider(<TestComponent />)
    
    await waitFor(() => {
      expect(screen.getByTestId('user')).toHaveTextContent('no-user')
    })

    // Token inválido deve ser removido
    expect(localStorage.removeItem).toHaveBeenCalledWith('token')
  })

  it('handles network errors gracefully', async () => {
    global.fetch = vi.fn(() => Promise.reject(new Error('Network error')))

    renderWithAuthProvider(<TestComponent />)
    
    const loginButton = screen.getByText('Login')
    loginButton.click()
    
    await waitFor(() => {
      expect(screen.getByTestId('user')).toHaveTextContent('no-user')
      expect(screen.getByTestId('loading')).toHaveTextContent('not-loading')
    })
  })

  it('provides correct authentication state', async () => {
    const TestAuthState = () => {
      const { isAuthenticated, user } = useAuth()
      return (
        <div>
          <div data-testid="is-authenticated">{isAuthenticated ? 'authenticated' : 'not-authenticated'}</div>
          <div data-testid="user-name">{user?.name || 'no-name'}</div>
        </div>
      )
    }

    renderWithAuthProvider(<TestAuthState />)
    
    // Inicialmente não autenticado
    expect(screen.getByTestId('is-authenticated')).toHaveTextContent('not-authenticated')
    expect(screen.getByTestId('user-name')).toHaveTextContent('no-name')
  })
})
