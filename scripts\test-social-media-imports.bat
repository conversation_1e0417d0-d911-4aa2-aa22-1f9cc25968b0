@echo off
echo ========================================
echo   TESTANDO IMPORTS DA SOCIAL MEDIA
echo ========================================
echo.

cd frontend

echo [1/4] Verificando imports do mediaService...
npx tsc --noEmit src/services/mediaService.ts

set MEDIA_RESULT=%errorlevel%

echo.
echo [2/4] Verificando imports do PostHistory...
npx tsc --noEmit src/components/social-media/PostHistory.tsx

set HISTORY_RESULT=%errorlevel%

echo.
echo [3/4] Verificando imports do PostScheduler...
npx tsc --noEmit src/components/social-media/PostScheduler.tsx

set SCHEDULER_RESULT=%errorlevel%

echo.
echo [4/4] Verificando imports do SocialMediaPage...
npx tsc --noEmit src/pages/SocialMediaPage.tsx

set PAGE_RESULT=%errorlevel%

echo.
echo ========================================
echo              RESULTADO
echo ========================================

if %MEDIA_RESULT% equ 0 (
    echo ✅ mediaService.ts - OK
) else (
    echo ❌ mediaService.ts - ERRO
)

if %HISTORY_RESULT% equ 0 (
    echo ✅ PostHistory.tsx - OK
) else (
    echo ❌ PostHistory.tsx - ERRO
)

if %SCHEDULER_RESULT% equ 0 (
    echo ✅ PostScheduler.tsx - OK
) else (
    echo ❌ PostScheduler.tsx - ERRO
)

if %PAGE_RESULT% equ 0 (
    echo ✅ SocialMediaPage.tsx - OK
) else (
    echo ❌ SocialMediaPage.tsx - ERRO
)

echo.
cd ..
pause
