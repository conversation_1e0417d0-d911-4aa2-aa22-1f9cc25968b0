{"name": "promandato", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "deploy:storage-rules": "firebase deploy --only storage", "emulators:start": "firebase emulators:start", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:watch": "vitest --watch"}, "dependencies": {"@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.3.1", "bcryptjs": "^3.0.2", "date-fns": "^4.1.0", "exceljs": "^4.4.0", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "firebase": "^11.8.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lucide-react": "^0.513.0", "react": "^19.1.0", "react-big-calendar": "^1.19.2", "react-dom": "^19.1.0", "react-router-dom": "^7.6.0", "react-toastify": "^11.0.5", "recharts": "^2.15.3", "socket.io-client": "^4.8.1", "stripe": "^18.2.1", "xlsx": "^0.18.5"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@testing-library/user-event": "^14.5.2", "@types/exceljs": "^1.3.2", "@types/jspdf": "^1.3.3", "@types/node": "^22.14.0", "@types/react": "^19.1.5", "@types/react-dom": "^19.1.5", "@types/xlsx": "^0.0.35", "@vitejs/plugin-react": "^4.5.1", "@vitest/coverage-v8": "^2.1.8", "@vitest/ui": "^2.1.8", "firebase-tools": "^14.4.0", "jsdom": "^26.0.0", "typescript": "~5.7.2", "vite": "^6.2.0", "vitest": "^2.1.8"}}