import { test, expect } from '@playwright/test';

test.describe('Landing Page - Funcionalidade Básica', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/landingpage');
  });

  test('deve carregar a landing page corretamente', async ({ page }) => {
    // Verificar se o título está correto
    await expect(page).toHaveTitle(/Pro-Mandato/);
    
    // Verificar se elementos principais estão visíveis
    await expect(page.locator('h1')).toBeVisible();
    await expect(page.locator('nav')).toBeVisible();
    
    // Verificar se não há erros de console críticos
    const errors: string[] = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      }
    });
    
    await page.waitForLoadState('networkidle');
    
    // Filtrar erros conhecidos/esperados
    const criticalErrors = errors.filter(error => 
      !error.includes('favicon') && 
      !error.includes('analytics') &&
      !error.includes('CORS')
    );
    
    expect(criticalErrors).toHaveLength(0);
  });

  test('deve exibir seções principais', async ({ page }) => {
    // Verificar seções principais
    await expect(page.locator('#hero')).toBeVisible();
    await expect(page.locator('#features')).toBeVisible();
    await expect(page.locator('#pricing')).toBeVisible();
    await expect(page.locator('#contact')).toBeVisible();
  });

  test('deve ter navegação funcional', async ({ page }) => {
    // Testar links de navegação
    await page.click('a[href="#features"]');
    await expect(page.locator('#features')).toBeInViewport();
    
    await page.click('a[href="#pricing"]');
    await expect(page.locator('#pricing')).toBeInViewport();
    
    await page.click('a[href="#contact"]');
    await expect(page.locator('#contact')).toBeInViewport();
  });

  test('deve ser responsivo', async ({ page }) => {
    // Testar em diferentes tamanhos de tela
    
    // Desktop
    await page.setViewportSize({ width: 1920, height: 1080 });
    await expect(page.locator('nav')).toBeVisible();
    
    // Tablet
    await page.setViewportSize({ width: 768, height: 1024 });
    await expect(page.locator('nav')).toBeVisible();
    
    // Mobile
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Verificar se o menu mobile funciona
    const mobileMenuButton = page.locator('[data-testid="mobile-menu-button"]');
    if (await mobileMenuButton.isVisible()) {
      await mobileMenuButton.click();
      await expect(page.locator('[data-testid="mobile-menu"]')).toBeVisible();
    }
  });

  test('deve carregar imagens corretamente', async ({ page }) => {
    // Aguardar todas as imagens carregarem
    await page.waitForLoadState('networkidle');
    
    const images = page.locator('img');
    const imageCount = await images.count();
    
    for (let i = 0; i < imageCount; i++) {
      const img = images.nth(i);
      const src = await img.getAttribute('src');
      
      if (src && !src.startsWith('data:')) {
        // Verificar se a imagem carregou sem erro
        const naturalWidth = await img.evaluate((el: HTMLImageElement) => el.naturalWidth);
        expect(naturalWidth).toBeGreaterThan(0);
      }
    }
  });

  test('deve ter performance adequada', async ({ page }) => {
    const startTime = Date.now();
    
    await page.goto('/landingpage');
    await page.waitForLoadState('networkidle');
    
    const loadTime = Date.now() - startTime;
    
    // A página deve carregar em menos de 5 segundos
    expect(loadTime).toBeLessThan(5000);
    
    // Verificar métricas de performance
    const performanceMetrics = await page.evaluate(() => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      return {
        domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
        loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
        firstPaint: performance.getEntriesByName('first-paint')[0]?.startTime || 0,
        firstContentfulPaint: performance.getEntriesByName('first-contentful-paint')[0]?.startTime || 0
      };
    });
    
    // DOM deve carregar em menos de 2 segundos
    expect(performanceMetrics.domContentLoaded).toBeLessThan(2000);
  });

  test('deve ter acessibilidade básica', async ({ page }) => {
    // Verificar se há elementos com aria-labels apropriados
    const buttons = page.locator('button');
    const buttonCount = await buttons.count();
    
    for (let i = 0; i < buttonCount; i++) {
      const button = buttons.nth(i);
      const ariaLabel = await button.getAttribute('aria-label');
      const textContent = await button.textContent();
      
      // Botão deve ter aria-label ou texto visível
      expect(ariaLabel || textContent?.trim()).toBeTruthy();
    }
    
    // Verificar se há headings em ordem hierárquica
    const h1Count = await page.locator('h1').count();
    expect(h1Count).toBe(1); // Deve haver exatamente um H1
    
    // Verificar se links têm texto descritivo
    const links = page.locator('a');
    const linkCount = await links.count();
    
    for (let i = 0; i < linkCount; i++) {
      const link = links.nth(i);
      const href = await link.getAttribute('href');
      const textContent = await link.textContent();
      const ariaLabel = await link.getAttribute('aria-label');
      
      if (href && href !== '#') {
        expect(textContent?.trim() || ariaLabel).toBeTruthy();
      }
    }
  });

  test('deve funcionar sem JavaScript (graceful degradation)', async ({ page }) => {
    // Desabilitar JavaScript
    await page.context().addInitScript(() => {
      Object.defineProperty(window, 'navigator', {
        value: { ...window.navigator, javaEnabled: () => false }
      });
    });
    
    await page.goto('/landingpage');
    
    // Verificar se o conteúdo principal ainda é visível
    await expect(page.locator('h1')).toBeVisible();
    await expect(page.locator('#features')).toBeVisible();
    await expect(page.locator('#pricing')).toBeVisible();
    
    // Links de navegação ainda devem funcionar (âncoras)
    await page.click('a[href="#features"]');
    await expect(page.locator('#features')).toBeInViewport();
  });
});
