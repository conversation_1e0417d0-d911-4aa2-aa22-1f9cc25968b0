
import React, { useState, useEffect, useCallback } from 'react';
import { Card } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import { Modal } from '../components/ui/Modal';
import { Input } from '../components/ui/Input';
import { ICONS } from '../constants';
import { Document } from '../types';
import { LoadingSpinner } from '../components/ui/LoadingSpinner';
import { useAuth } from '../hooks/useAuth';
import { 
  getDocumentsMetadata, 
  addDocumentMetadata, 
  deleteDocumentAndFile,
  uploadFileToStorage 
} from '../services/firebaseService';
import { doc, collection } from 'firebase/firestore'; 
import { firestoreDb as db } from '../services/firebaseService'; 

const formatBytes = (bytes: number, decimals = 2) => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

const DocumentsPage: React.FC = () => {
  const [documents, setDocuments] = useState<Document[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [documentName, setDocumentName] = useState('');
  const [isUploading, setIsUploading] = useState(false);

  const { currentUser } = useAuth();

  const fetchDocuments = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      const fetchedDocs = await getDocumentsMetadata();
      setDocuments(fetchedDocs);
    } catch (err) {
      console.error("Failed to fetch documents:", err);
      setError("Falha ao carregar documentos. Tente novamente.");
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchDocuments();
  }, [fetchDocuments]);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      const file = event.target.files[0];
      if (file.size > 10 * 1024 * 1024) { 
          alert("O arquivo é muito grande. Máximo 10MB.");
          event.target.value = ''; 
          return;
      }
      setSelectedFile(file);
      setDocumentName(file.name);
    } else {
        setSelectedFile(null);
        setDocumentName('');
    }
  };

  const handleOpenModal = () => {
    setSelectedFile(null);
    setDocumentName('');
    setError(null);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedFile(null);
    setDocumentName('');
  };

  const handleUploadDocument = async () => {
    if (!selectedFile || !currentUser) {
      setError("Selecione um arquivo e certifique-se de estar logado.");
      return;
    }

    setIsUploading(true);
    setError(null);

    try {
      const newDocRef = doc(collection(db, "documents"));
      const docId = newDocRef.id;

      // Create a unique filename with timestamp and docId to avoid conflicts
      const timestamp = Date.now();
      const uniqueFileName = `${timestamp}_${docId}_${selectedFile.name}`;
      const timestampedStoragePath = `documents/${currentUser.id}/${uniqueFileName}`;
      const finalDownloadURL = await uploadFileToStorage(selectedFile, timestampedStoragePath);
      
      const finalMetadata: Omit<Document, 'id' | 'createdAt'> = {
        name: documentName || selectedFile.name,
        type: selectedFile.type || 'application/octet-stream',
        url: finalDownloadURL,
        storagePath: timestampedStoragePath, 
        uploadedBy: currentUser.id,
        size: selectedFile.size,
      };

      await addDocumentMetadata(finalMetadata);
      
      await fetchDocuments(); 
      handleCloseModal();
    } catch (err) {
      console.error("Failed to upload document:", err);
      setError("Falha ao enviar documento. Tente novamente.");
    } finally {
      setIsUploading(false);
    }
  };

  const handleDeleteDocument = async (docToDelete: Document) => {
    if (window.confirm(`Tem certeza que deseja excluir o documento "${docToDelete.name}"? A ação é irreversível.`)) {
      setIsLoading(true); 
      setError(null);
      try {
        await deleteDocumentAndFile(docToDelete.id, docToDelete.storagePath);
        await fetchDocuments();
      } catch (err) {
        console.error("Failed to delete document:", err);
        setError("Falha ao excluir documento. Tente novamente.");
      } finally {
        setIsLoading(false);
      }
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold text-neutral-dark dark:text-neutral-light">Gerenciamento de Documentos</h1>
        <Button onClick={handleOpenModal} leftIcon={ICONS.PLUS} disabled={isLoading || isUploading}>
          Novo Documento
        </Button>
      </div>

      {error && !isModalOpen && <p className="text-red-500 dark:text-red-400 bg-red-100 dark:bg-red-900 dark:bg-opacity-30 p-3 rounded-md">{error}</p>}

      <Card>
        {isLoading && !documents.length ? (
            <div className="flex justify-center items-center py-10">
                <LoadingSpinner size="lg" />
            </div>
        ) : (
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-neutral-dark">
            <thead className="bg-gray-50 dark:bg-neutral-darker">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-neutral-DEFAULT uppercase tracking-wider">Nome do Arquivo</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-neutral-DEFAULT uppercase tracking-wider">Tipo</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-neutral-DEFAULT uppercase tracking-wider">Tamanho</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-neutral-DEFAULT uppercase tracking-wider">Data de Envio</th>
                <th scope="col" className="relative px-6 py-3"><span className="sr-only">Ações</span></th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-neutral-darker divide-y divide-gray-200 dark:divide-neutral-dark">
              {documents.length > 0 ? documents.map((doc) => (
                <tr key={doc.id} className="hover:bg-gray-50 dark:hover:bg-neutral-dark">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-primary dark:text-primary-light hover:underline">
                    <a href={doc.url} target="_blank" rel="noopener noreferrer" title={doc.name}>{doc.name.length > 50 ? `${doc.name.substring(0,47)}...` : doc.name}</a>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-neutral-DEFAULT">{doc.type.toUpperCase()}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-neutral-DEFAULT">{formatBytes(doc.size)}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-neutral-DEFAULT">{new Date(doc.createdAt).toLocaleDateString('pt-BR')}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <Button 
                        variant="ghost" 
                        size="sm" 
                        onClick={() => handleDeleteDocument(doc)} 
                        className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-500"
                        aria-label={`Excluir ${doc.name}`}
                        disabled={isLoading || isUploading}
                    >
                      {React.cloneElement(ICONS.TRASH, {className: "w-5 h-5"})}
                    </Button>
                  </td>
                </tr>
              )) : (
                <tr>
                    <td colSpan={5} className="px-6 py-4 text-center text-sm text-gray-500 dark:text-neutral-DEFAULT">
                        Nenhum documento cadastrado.
                    </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
        )}
      </Card>

      <Modal isOpen={isModalOpen} onClose={handleCloseModal} title="Enviar Novo Documento">
        <div className="space-y-4">
          {error && <p className="text-red-500 dark:text-red-400 bg-red-100 dark:bg-red-900 dark:bg-opacity-30 p-2 rounded-md text-sm">{error}</p>}
          <div>
            <label htmlFor="file-upload" className="block text-sm font-medium text-neutral-dark dark:text-neutral-light mb-1">
              Selecionar Arquivo (Max 10MB)
            </label>
            <Input
              id="file-upload"
              type="file"
              onChange={handleFileChange}
              className="block w-full text-sm text-gray-500 dark:text-neutral-DEFAULT 
                         file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 
                         file:text-sm file:font-semibold 
                         file:bg-primary-light file:text-primary 
                         dark:file:bg-primary-dark dark:file:text-neutral-extralight
                         hover:file:bg-primary dark:hover:file:bg-primary-light"
              disabled={isUploading}
            />
            {selectedFile && <p className="text-xs text-gray-500 dark:text-neutral-DEFAULT mt-1">{selectedFile.name} ({formatBytes(selectedFile.size)})</p>}
          </div>
          <Input
            label="Nome do Documento (opcional, se diferente do arquivo)"
            value={documentName}
            onChange={(e) => setDocumentName(e.target.value)}
            placeholder="Deixe em branco para usar o nome do arquivo"
            disabled={isUploading}
          />
          <div className="flex justify-end space-x-2 pt-4">
            <Button variant="outline" onClick={handleCloseModal} disabled={isUploading}>Cancelar</Button>
            <Button onClick={handleUploadDocument} disabled={!selectedFile || isUploading} isLoading={isUploading}>
              {isUploading ? "Enviando..." : "Enviar Documento"}
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default DocumentsPage;