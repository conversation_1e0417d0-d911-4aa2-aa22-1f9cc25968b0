@echo off
echo ========================================
echo     VERIFICANDO CONFIGURACAO DE TESTES
echo ========================================
echo.

echo [1/4] Verificando Backend (Jest)...
cd backend
if exist "node_modules\jest" (
    echo ✅ Jest instalado
    npx jest --version
) else (
    echo ❌ Jest NAO instalado
    echo Execute: scripts\install-backend-tests.bat
)
cd ..

echo.
echo [2/4] Verificando Frontend (Vitest)...
cd frontend
if exist "node_modules\vitest" (
    echo ✅ Vitest instalado
    npx vitest --version
) else (
    echo ❌ Vitest NAO instalado
    echo Execute: scripts\install-frontend-tests.bat
)
cd ..

echo.
echo [3/4] Verificando E2E (Playwright)...
if exist "node_modules\@playwright\test" (
    echo ✅ Playwright instalado
    npx playwright --version
) else (
    echo ❌ Playwright NAO instalado
    echo Execute: scripts\install-e2e-tests.bat
)

echo.
echo [4/4] Executando teste simples do backend...
cd backend
npx jest tests/simple.test.js --silent
if %errorlevel% equ 0 (
    echo ✅ Teste simples do backend PASSOU
) else (
    echo ❌ Teste simples do backend FALHOU
)
cd ..

echo.
echo ========================================
echo           RESUMO DA VERIFICACAO
echo ========================================
echo.

echo Ferramentas de teste:
if exist "backend\node_modules\jest" (
    echo ✅ Backend: Jest
) else (
    echo ❌ Backend: Jest NAO instalado
)

if exist "frontend\node_modules\vitest" (
    echo ✅ Frontend: Vitest
) else (
    echo ❌ Frontend: Vitest NAO instalado
)

if exist "node_modules\@playwright\test" (
    echo ✅ E2E: Playwright
) else (
    echo ❌ E2E: Playwright NAO instalado
)

echo.
echo Comandos disponiveis:
echo.
echo === BACKEND ===
echo - cd backend ^&^& npm run test
echo - cd backend ^&^& npm run test:watch
echo - cd backend ^&^& npm run test:coverage
echo.
echo === FRONTEND ===
echo - cd frontend ^&^& npm run test
echo - cd frontend ^&^& npm run test:ui
echo - cd frontend ^&^& npm run test:coverage
echo.
echo === E2E ===
echo - npm run test:e2e
echo - npm run test:e2e:ui
echo - npm run test:e2e:headed
echo.
echo === TODOS ===
echo - scripts\run-all-tests.bat
echo - scripts\run-tests-with-coverage.bat
echo.

pause
