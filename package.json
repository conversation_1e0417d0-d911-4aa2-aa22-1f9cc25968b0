{"name": "promandato-tests", "version": "1.0.0", "description": "Testes E2E para o projeto Pro-Mandato", "scripts": {"test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:e2e:debug": "playwright test --debug", "test:e2e:report": "playwright show-report", "test:landing": "playwright test tests/landing-page", "test:frontend": "cd frontend && npm run test", "test:backend": "cd backend && npm run test", "test:all": "npm run test:backend && npm run test:frontend && npm run test:e2e", "test:all:coverage": "npm run test:backend -- --coverage && npm run test:frontend -- --coverage", "install:all": "npm install && cd frontend && npm install && cd ../backend && npm install", "setup:tests": "npm run install:all && npx playwright install"}, "devDependencies": {"@playwright/test": "^1.49.1", "typescript": "^5.7.2"}, "keywords": ["tests", "e2e", "playwright", "promandato"], "author": "Pro-Mandato Team", "license": "MIT"}