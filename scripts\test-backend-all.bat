@echo off
echo ========================================
echo      EXECUTANDO TODOS OS TESTES - BACKEND
echo ========================================
echo.

echo [1/3] Navegando para o diretorio backend...
cd backend

echo.
echo [2/3] Verificando se Jest esta instalado...
if not exist "node_modules\jest" (
    echo ERRO: Jest nao encontrado!
    echo Execute: scripts\install-backend-tests.bat
    pause
    exit /b 1
)

echo Jest encontrado!

echo.
echo [3/3] Executando todos os testes...
npx jest --verbose

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo      TODOS OS TESTES PASSARAM! 🎉
    echo ========================================
    echo.
    echo Comandos disponiveis:
    echo - npx jest --watch (modo watch)
    echo - npx jest --coverage (com cobertura)
    echo - npx jest tests/unit (apenas unitarios)
    echo - npx jest tests/api (apenas API)
) else (
    echo.
    echo ========================================
    echo        ALGUNS TESTES FALHARAM! ❌
    echo ========================================
    echo.
    echo Verifique os erros acima.
    echo.
    echo Para executar testes especificos:
    echo - npx jest tests/simple.test.js
    echo - npx jest --testNamePattern="nome do teste"
)

echo.
cd ..
pause
