#!/bin/bash

echo "========================================"
echo "   DEBUG: CONFIGURAÇÕES DE IA"
echo "========================================"
echo

cd frontend

echo "[1/2] Iniciando servidor com debug..."
echo
echo "========================================"
echo "      🔍 DEBUG DAS CONFIGURAÇÕES"
echo "========================================"
echo
echo "PROBLEMA REPORTADO:"
echo "- Alert mostra 'Configurações do sistema salvas (simulado)!'"
echo "- Deveria mostrar salvamento real das configurações"
echo
echo "PASSOS PARA DEBUG:"
echo
echo "1. ABRA O CONSOLE DO NAVEGADOR:"
echo "   - Pressione F12"
echo "   - Vá para aba 'Console'"
echo "   - Mantenha aberto durante os testes"
echo
echo "2. ACESSE AS CONFIGURAÇÕES:"
echo "   - Vá para: http://localhost:5173/ai/settings"
echo "   - Observe os logs no console"
echo
echo "3. CONFIGURE A API:"
echo "   - Selecione 'Google Gemini'"
echo "   - Cole uma chave API válida"
echo "   - Clique 'Testar' (observe logs)"
echo "   - Clique 'Salvar Configurações' (observe logs)"
echo
echo "4. VERIFIQUE OS LOGS:"
echo "   - Procure por: '🔧 Salvando configurações de IA:'"
echo "   - Procure por: '✅ Configurações de IA salvas com sucesso!'"
echo "   - Procure por: '🔍 Configurações carregadas após salvar:'"
echo
echo "5. TESTE A IA:"
echo "   - Vá para: http://localhost:5173/ai"
echo "   - Teste 'Análise de Sentimento'"
echo "   - Observe se usa IA real ou simulada"
echo
echo "========================================"
echo "      📋 O QUE VERIFICAR"
echo "========================================"
echo
echo "✅ SALVAMENTO CORRETO SE:"
echo "   - Console mostra logs de salvamento"
echo "   - Alert mostra 'Provedor: gemini'"
echo "   - Não aparece '(simulado)' no alert"
echo
echo "❌ PROBLEMA SE:"
echo "   - Alert continua mostrando '(simulado)'"
echo "   - Console mostra erros"
echo "   - IA continua usando dados simulados"
echo
echo "🔍 POSSÍVEIS CAUSAS:"
echo "   - Outro componente mostrando alert"
echo "   - Cache do navegador"
echo "   - Erro no Firestore"
echo "   - Problema de autenticação"
echo
echo "========================================"
echo "      🚀 SERVIDOR INICIANDO..."
echo "========================================"
echo
echo "Pressione Ctrl+C para parar o servidor"
echo

npm run dev

echo
cd ..
read -p "Pressione Enter para continuar..."
