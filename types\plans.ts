// Este arquivo é gerado automaticamente pelo dashboard de administração
// Não edite manualmente - use o dashboard para fazer alterações

export enum PlanType {
  BASIC = 'BASIC',
  STANDARD = 'STANDARD',
  PROFESSIONAL = 'PROFESSIONAL'
}

export interface PlanPrice {
  monthly: number;
  yearly: number;
}

export interface AIFeature {
  id: string;
  name: string;
  description: string;
  category: string;
  enabled: boolean;
}

export interface PlanFeatures {
  maxUsers: number; // -1 para ilimitado
  maxDemands: number; // -1 para ilimitado
  maxCitizens: number; // -1 para ilimitado
  storageGB: number;
  bulkMessages: boolean;
  socialMediaIntegration: boolean;
  smsNotifications: boolean;
  advancedReports: boolean;
  customReports: boolean;
  dataExport: boolean;
  apiAccess: boolean;
  webhooks: boolean;
  supportLevel: 'basic' | 'priority' | 'dedicated';
  aiFeatures: AIFeature[];
}

export interface Plan {
  id: PlanType;
  name: string;
  description: string;
  price: PlanPrice;
  features: PlanFeatures;
  enabled: boolean;
  popular?: boolean;
  badge?: string;
  createdAt: string;
  updatedAt: string;
}

// Lista completa de features de IA disponíveis
export const AI_FEATURES: AIFeature[] = [
  {
    "id": "text-analysis",
    "name": "Análise de Texto",
    "description": "Análise automática de sentimentos em demandas",
    "category": "Análise",
    "enabled": true
  },
  {
    "id": "auto-categorization",
    "name": "Categorização Automática",
    "description": "Categorização inteligente de demandas",
    "category": "Automação",
    "enabled": true
  },
  {
    "id": "predictive-analytics",
    "name": "Análise Preditiva",
    "description": "Previsões baseadas em dados históricos",
    "category": "Análise",
    "enabled": true
  },
  {
    "id": "smart-responses",
    "name": "Respostas Inteligentes",
    "description": "Sugestões automáticas de respostas",
    "category": "Automação",
    "enabled": true
  }
];

// Configuração atual dos planos (atualizada automaticamente)
export const PLANS: Plan[] = [
  {
    "id": "BASIC",
    "name": "Básico",
    "description": "Ideal para pequenas equipes e projetos iniciais",
    "price": {
      "monthly": 169.9,
      "yearly": 1834.92
    },
    "features": {
      "maxUsers": 5,
      "maxDemands": 100,
      "maxCitizens": 500,
      "storageGB": 10,
      "bulkMessages": false,
      "socialMediaIntegration": false,
      "smsNotifications": false,
      "advancedReports": false,
      "customReports": false,
      "dataExport": true,
      "apiAccess": false,
      "webhooks": false,
      "supportLevel": "basic",
      "aiFeatures": []
    },
    "enabled": true,
    "popular": false,
    "createdAt": "2025-06-06T15:07:43.261Z",
    "updatedAt": "2025-06-08T19:04:19.986Z"
  },
  {
    "id": "STANDARD",
    "name": "Padrão",
    "description": "Para equipes em crescimento que precisam de mais recursos",
    "price": {
      "monthly": 259.9,
      "yearly": 2807.08
    },
    "features": {
      "maxUsers": 15,
      "maxDemands": 500,
      "maxCitizens": 2000,
      "storageGB": 50,
      "bulkMessages": true,
      "socialMediaIntegration": true,
      "smsNotifications": false,
      "advancedReports": true,
      "customReports": false,
      "dataExport": true,
      "apiAccess": true,
      "webhooks": false,
      "supportLevel": "priority",
      "aiFeatures": [
        {
          "id": "text-analysis",
          "name": "Análise de Texto",
          "description": "Análise automática de sentimentos em demandas",
          "category": "Análise",
          "enabled": true
        }
      ]
    },
    "enabled": true,
    "popular": true,
    "createdAt": "2025-06-06T15:07:43.262Z",
    "updatedAt": "2025-06-06T15:48:41.126Z"
  },
  {
    "id": "PROFESSIONAL",
    "name": "Profissional",
    "description": "Solução completa com IA avançada para grandes organizações",
    "price": {
      "monthly": 599.9,
      "yearly": 6478.92
    },
    "features": {
      "maxUsers": -1,
      "maxDemands": -1,
      "maxCitizens": -1,
      "storageGB": 200,
      "bulkMessages": true,
      "socialMediaIntegration": true,
      "smsNotifications": true,
      "advancedReports": true,
      "customReports": true,
      "dataExport": true,
      "apiAccess": true,
      "webhooks": true,
      "supportLevel": "dedicated",
      "aiFeatures": [
        {
          "id": "text-analysis",
          "name": "Análise de Texto",
          "description": "Análise automática de sentimentos em demandas",
          "category": "Análise",
          "enabled": true
        },
        {
          "id": "auto-categorization",
          "name": "Categorização Automática",
          "description": "Categorização inteligente de demandas",
          "category": "Automação",
          "enabled": true
        },
        {
          "id": "predictive-analytics",
          "name": "Análise Preditiva",
          "description": "Previsões baseadas em dados históricos",
          "category": "Análise",
          "enabled": true
        },
        {
          "id": "smart-responses",
          "name": "Respostas Inteligentes",
          "description": "Sugestões automáticas de respostas",
          "category": "Automação",
          "enabled": true
        }
      ]
    },
    "enabled": true,
    "popular": false,
    "badge": "IA Incluída",
    "createdAt": "2025-06-06T15:07:43.262Z",
    "updatedAt": "2025-06-06T15:49:01.266Z"
  }
];

// Função para obter um plano por ID
export function getPlanById(planId: PlanType): Plan | undefined {
  return PLANS.find(plan => plan.id === planId);
}

// Função para obter apenas planos ativos
export function getActivePlans(): Plan[] {
  return PLANS.filter(plan => plan.enabled);
}

// Última atualização
export const LAST_UPDATED = '2025-06-18T23:48:11.169Z';
