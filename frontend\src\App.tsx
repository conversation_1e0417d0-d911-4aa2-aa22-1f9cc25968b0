
import React from 'react';
import { HashRouter, Routes, Route } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import { ThemeProvider } from './contexts/ThemeContext';
import { PlanProvider } from './contexts/PlanContext';
import { NotificationProvider } from './contexts/NotificationContext';
import { Layout } from './components/layout/Layout';
import { ProtectedRoute } from './components/auth/ProtectedRoute';
import LoginPage from './pages/LoginPage';
import DashboardPage from './pages/DashboardPage';
import DemandsPage from './pages/DemandsPage';
import CitizensPage from './pages/CitizensPage';
import AgendaPage from './pages/AgendaPage';
import TeamPage from './pages/TeamPage';
import SettingsPage from './pages/SettingsPage';
import DocumentsPage from './pages/DocumentsPage';
import SocialMediaPage from './pages/SocialMediaPage';
import ReportsPage from './pages/ReportsPage';
import ProfilePage from './pages/ProfilePage';
import WorkspaceSettingsPage from './pages/WorkspaceSettingsPage';
import PoliticianProfilePage from './pages/PoliticianProfilePage';

import AIPage from './pages/AIPage';
import AISettingsPage from './pages/AISettingsPage';
import NotFoundPage from './pages/NotFoundPage';
import BulkMessagesPage from './pages/BulkMessagesPage';
import NotificationsTestPage from './pages/NotificationsTestPage';
// Dashboard administrativo
import AdminDashboardPage from './pages/AdminDashboardPage';
import { ROUTE_PATHS } from './constants';
import { UserRole } from './types';

const App: React.FC = () => {
  return (
    <AuthProvider>
      <ThemeProvider>
        <PlanProvider>
          <NotificationProvider>
            <HashRouter>
            <Routes>
              <Route path={ROUTE_PATHS.LOGIN} element={<LoginPage />} />
              <Route 
                path="/" 
                element={
                  <ProtectedRoute>
                    <Layout />
                  </ProtectedRoute>
                }
              >
              <Route index element={<DashboardPage />} /> 
              <Route path={ROUTE_PATHS.DEMANDS} element={<DemandsPage />} />
              <Route path={ROUTE_PATHS.CITIZENS} element={<CitizensPage />} />
              <Route path={ROUTE_PATHS.AGENDA} element={<AgendaPage />} />
              <Route 
                path={ROUTE_PATHS.TEAM} 
                element={
                  <ProtectedRoute allowedRoles={[UserRole.ADMIN]}>
                    <TeamPage />
                  </ProtectedRoute>
                } 
              />
              <Route 
                path={ROUTE_PATHS.WORKSPACE_SETTINGS} 
                element={
                  <ProtectedRoute allowedRoles={[UserRole.ADMIN]}>
                    <WorkspaceSettingsPage />
                  </ProtectedRoute>
                } 
              />
              <Route path={ROUTE_PATHS.DOCUMENTS} element={<DocumentsPage />} />
              <Route path={ROUTE_PATHS.SOCIAL_MEDIA} element={<SocialMediaPage />} />
              <Route path={ROUTE_PATHS.REPORTS} element={<ReportsPage />} />
              <Route path={ROUTE_PATHS.SETTINGS} element={<SettingsPage />} />
              <Route path="/settings/plan" element={<SettingsPage />} />
              <Route path={ROUTE_PATHS.PROFILE} element={<ProfilePage />} />
              <Route path="/ai/settings" element={<AISettingsPage />} />
              <Route path={ROUTE_PATHS.AI} element={<AIPage />} />
              <Route
                path={ROUTE_PATHS.POLITICIAN_PROFILE}
                element={
                  <ProtectedRoute>
                    <PoliticianProfilePage />
                  </ProtectedRoute>
                }
              />
              <Route
                path={ROUTE_PATHS.BULK_MESSAGES}
                element={
                  <ProtectedRoute>
                    <BulkMessagesPage />
                  </ProtectedRoute>
                }
              />
              {/* Rota do Dashboard Administrativo */}
              <Route
                path="/admin"
                element={
                  <ProtectedRoute allowedRoles={[UserRole.ADMIN]}>
                    <AdminDashboardPage />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/notifications-test"
                element={
                  <ProtectedRoute>
                    <NotificationsTestPage />
                  </ProtectedRoute>
                }
              />
              <Route path="*" element={<NotFoundPage />} />
            </Route>
          </Routes>
            </HashRouter>
          </NotificationProvider>
        </PlanProvider>
      </ThemeProvider>
    </AuthProvider>
);
};

export default App;
