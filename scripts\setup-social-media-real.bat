@echo off
echo ========================================
echo   CONFIGURACAO SOCIAL MEDIA - DADOS REAIS
echo ========================================
echo.

cd frontend

echo [1/4] Verificando build...
npm run build > build_log.txt 2>&1

if %errorlevel% neq 0 (
    echo ❌ Build falhou! Verificando erros:
    type build_log.txt
    pause
    exit /b 1
)

echo ✅ Build OK!

echo.
echo [2/4] Iniciando servidor...
echo.
echo ========================================
echo      🚀 SOCIAL MEDIA PRONTO!
echo ========================================
echo.
echo CONFIGURACAO PARA DADOS REAIS:
echo.
echo 📋 PASSO A PASSO:
echo.
echo 1. CONFIGURE SEU PERFIL:
echo    - Acesse: http://localhost:5173/politician-profile
echo    - Preencha seus dados de redes sociais:
echo      * Instagram: @seu_usuario
echo      * Facebook: sua_pagina_facebook
echo      * Twitter: @seu_twitter
echo      * TikTok: @seu_tiktok
echo    - Salve as alteracoes
echo.
echo 2. CONECTE SUAS CONTAS:
echo    - Acesse: http://localhost:5173/social-media
echo    - Clique nos botoes "Conectar [Rede Social]"
echo    - Suas contas serao conectadas automaticamente
echo.
echo 3. TESTE AS FUNCIONALIDADES:
echo    - Escreva um post de teste
echo    - Selecione as redes sociais
echo    - Escolha "Publicar agora" ou "Agendar"
echo    - Clique em "Publicar" ou "Agendar Publicacao"
echo.
echo 4. VERIFIQUE O HISTORICO:
echo    - Va para aba "Historico"
echo    - Seus posts aparecerao la
echo    - Teste republicar posts
echo.
echo ========================================
echo      📱 FUNCIONALIDADES IMPLEMENTADAS
echo ========================================
echo.
echo ✅ Conexao real com perfil do politico
echo ✅ Salvamento de posts no Firestore
echo ✅ Historico de publicacoes real
echo ✅ Agendamento de posts
echo ✅ Upload de midia para Firebase Storage
echo ✅ Interface profissional com abas
echo ✅ Validacoes e tratamento de erros
echo.
echo ========================================
echo      🔧 PROXIMOS PASSOS OPCIONAIS
echo ========================================
echo.
echo Para implementar APIs reais das redes sociais:
echo 1. Facebook Graph API
echo 2. Instagram Basic Display API
echo 3. Twitter API v2
echo 4. TikTok for Developers API
echo.
echo Por enquanto, as publicacoes sao simuladas
echo mas todos os dados sao salvos corretamente.
echo.
echo Pressione Ctrl+C para parar o servidor
echo.

npm run dev

echo.
cd ..
pause
