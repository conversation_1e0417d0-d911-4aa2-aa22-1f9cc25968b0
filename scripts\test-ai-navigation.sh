#!/bin/bash

echo "========================================"
echo "   TESTE: NAVEGAÇÃO AI SETTINGS"
echo "========================================"
echo

cd frontend

echo "[1/2] Iniciando servidor..."
echo
echo "========================================"
echo "      🧪 TESTE DE NAVEGAÇÃO"
echo "========================================"
echo
echo "PROBLEMA REPORTADO:"
echo "- Botão 'Configurações de IA' vai para /ai/settings"
echo "- Mas abre o painel principal em vez da página de configurações"
echo
echo "CORREÇÕES APLICADAS:"
echo "✅ Rota /ai/settings movida antes de /ai no App.tsx"
echo "✅ Botão usa navigate() em vez de window.location.href"
echo "✅ Build compilando sem erros"
echo
echo "PASSOS PARA TESTAR:"
echo
echo "1. ACESSE A PÁGINA DE IA:"
echo "   - Vá para: http://localhost:5173/ai"
echo "   - Verifique se a página carrega corretamente"
echo
echo "2. TESTE O BOTÃO CONFIGURAÇÕES:"
echo "   - Clique no botão '⚙️ Configurações de IA' (canto superior direito)"
echo "   - URL deve mudar para: http://localhost:5173/ai/settings"
echo "   - Página deve mostrar 'Configurações de IA' com campos de API"
echo
echo "3. TESTE NAVEGAÇÃO DIRETA:"
echo "   - Digite diretamente: http://localhost:5173/ai/settings"
echo "   - Deve abrir a página de configurações"
echo
echo "4. TESTE NAVEGAÇÃO DE VOLTA:"
echo "   - Na página de configurações, volte para /ai"
echo "   - Teste novamente o botão"
echo
echo "========================================"
echo "      📋 O QUE VERIFICAR"
echo "========================================"
echo
echo "✅ FUNCIONANDO CORRETAMENTE SE:"
echo "   - Botão leva para página de configurações"
echo "   - URL mostra /ai/settings"
echo "   - Página tem título 'Configurações de IA'"
echo "   - Campos de API Key estão visíveis"
echo "   - Dropdown 'Provedor de IA' está presente"
echo
echo "❌ AINDA COM PROBLEMA SE:"
echo "   - Botão leva para painel principal"
echo "   - URL não muda ou muda incorretamente"
echo "   - Página não carrega configurações"
echo "   - Erro 404 ou página em branco"
echo
echo "🔍 POSSÍVEIS SOLUÇÕES ADICIONAIS:"
echo "   - Limpar cache do navegador (Ctrl+Shift+R)"
echo "   - Testar em aba anônima"
echo "   - Verificar console do navegador (F12)"
echo
echo "========================================"
echo "      🚀 SERVIDOR INICIANDO..."
echo "========================================"
echo
echo "Pressione Ctrl+C para parar o servidor"
echo

npm run dev

echo
cd ..
read -p "Pressione Enter para continuar..."
