import React, { useState, useEffect } from 'react';
import { ICONS } from '../../constants';
import { usePlan } from '../../hooks/usePlan';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';

interface PredictiveInsight {
  id: string;
  title: string;
  description: string;
  type: 'trend' | 'forecast' | 'anomaly' | 'recommendation';
  confidence: number;
  impact: 'low' | 'medium' | 'high';
  timeframe: string;
  data: {
    current: number;
    predicted: number;
    change: number;
  };
  actionItems: string[];
}

interface PredictiveData {
  demandTrends: PredictiveInsight[];
  citizenBehavior: PredictiveInsight[];
  resourceAllocation: PredictiveInsight[];
  performanceMetrics: PredictiveInsight[];
}

const PredictiveAnalytics: React.FC = () => {
  const { canUseAIFeature, getAIFeatureUsage, incrementAIUsage } = usePlan();
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [insights, setInsights] = useState<PredictiveData | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<keyof PredictiveData>('demandTrends');
  const [error, setError] = useState<string | null>(null);

  const usage = getAIFeatureUsage('predictive-analytics');

  useEffect(() => {
    // Carregar dados iniciais
    if (canUseAIFeature('predictive-analytics')) {
      loadPredictiveData();
    }
  }, [canUseAIFeature]);

  const loadPredictiveData = async () => {
    setIsAnalyzing(true);
    setError(null);

    try {
      // Simular análise preditiva
      await new Promise(resolve => setTimeout(resolve, 3000));

      const mockData: PredictiveData = {
        demandTrends: [
          {
            id: '1',
            title: 'Aumento de Demandas de Saúde',
            description: 'Previsão de aumento de 35% nas demandas relacionadas à saúde nos próximos 30 dias',
            type: 'forecast',
            confidence: 0.87,
            impact: 'high',
            timeframe: 'Próximos 30 dias',
            data: { current: 156, predicted: 211, change: 35 },
            actionItems: [
              'Alocar mais recursos para o departamento de saúde',
              'Preparar equipe para aumento de demanda',
              'Revisar protocolos de atendimento'
            ]
          },
          {
            id: '2',
            title: 'Redução em Demandas de Infraestrutura',
            description: 'Tendência de queda de 15% nas demandas de infraestrutura',
            type: 'trend',
            confidence: 0.72,
            impact: 'medium',
            timeframe: 'Próximos 15 dias',
            data: { current: 89, predicted: 76, change: -15 },
            actionItems: [
              'Realocar recursos para outras áreas',
              'Aproveitar período para manutenção preventiva'
            ]
          }
        ],
        citizenBehavior: [
          {
            id: '3',
            title: 'Pico de Atividade às Segundas',
            description: 'Cidadãos tendem a enviar 40% mais demandas às segundas-feiras',
            type: 'trend',
            confidence: 0.91,
            impact: 'medium',
            timeframe: 'Padrão semanal',
            data: { current: 45, predicted: 63, change: 40 },
            actionItems: [
              'Aumentar equipe de plantão às segundas',
              'Implementar sistema de triagem automática'
            ]
          }
        ],
        resourceAllocation: [
          {
            id: '4',
            title: 'Sobrecarga no Setor Jurídico',
            description: 'Previsão de sobrecarga de 25% no setor jurídico baseada em tendências atuais',
            type: 'forecast',
            confidence: 0.78,
            impact: 'high',
            timeframe: 'Próximos 7 dias',
            data: { current: 120, predicted: 150, change: 25 },
            actionItems: [
              'Contratar consultoria jurídica temporária',
              'Priorizar casos mais urgentes',
              'Implementar sistema de agendamento'
            ]
          }
        ],
        performanceMetrics: [
          {
            id: '5',
            title: 'Melhoria no Tempo de Resposta',
            description: 'Tendência de melhoria de 20% no tempo médio de resposta',
            type: 'trend',
            confidence: 0.85,
            impact: 'medium',
            timeframe: 'Últimos 14 dias',
            data: { current: 2.5, predicted: 2.0, change: -20 },
            actionItems: [
              'Manter práticas atuais',
              'Documentar processos eficientes',
              'Treinar equipe com base nas melhorias'
            ]
          }
        ]
      };

      setInsights(mockData);
    } catch (error) {
      console.error('Erro na análise preditiva:', error);
      setError('Erro ao gerar análise preditiva. Tente novamente.');
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handleRefreshAnalysis = async () => {
    const canProceed = await incrementAIUsage('predictive-analytics');
    if (!canProceed) {
      setError('Limite de uso atingido para este mês');
      return;
    }
    
    await loadPredictiveData();
  };

  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'trend':
        return <div className="w-5 h-5 text-blue-500">{ICONS.TRENDING_UP}</div>;
      case 'forecast':
        return <div className="w-5 h-5 text-purple-500">{ICONS.BRAIN}</div>;
      case 'anomaly':
        return <div className="w-5 h-5 text-red-500">{ICONS.ALERT_CIRCLE}</div>;
      case 'recommendation':
        return <div className="w-5 h-5 text-green-500">{ICONS.LIGHTBULB}</div>;
      default:
        return <div className="w-5 h-5 text-gray-500">{ICONS.BRAIN}</div>;
    }
  };

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'high':
        return 'text-red-700 bg-red-50 border-red-200';
      case 'medium':
        return 'text-yellow-700 bg-yellow-50 border-yellow-200';
      case 'low':
        return 'text-green-700 bg-green-50 border-green-200';
      default:
        return 'text-gray-700 bg-gray-50 border-gray-200';
    }
  };

  const getImpactLabel = (impact: string) => {
    switch (impact) {
      case 'high':
        return 'Alto Impacto';
      case 'medium':
        return 'Médio Impacto';
      case 'low':
        return 'Baixo Impacto';
      default:
        return 'Impacto Desconhecido';
    }
  };

  const categories = [
    { key: 'demandTrends', name: 'Tendências de Demandas', icon: ICONS.TRENDING_UP },
    { key: 'citizenBehavior', name: 'Comportamento Cidadão', icon: ICONS.USERS },
    { key: 'resourceAllocation', name: 'Alocação de Recursos', icon: ICONS.PIE_CHART },
    { key: 'performanceMetrics', name: 'Métricas de Performance', icon: ICONS.BAR_CHART }
  ];

  if (!canUseAIFeature('predictive-analytics')) {
    return (
      <Card className="p-6">
        <div className="text-center py-8">
          <div className="bg-purple-100 rounded-full p-4 inline-block mb-4">
            <div className="w-8 h-8 text-purple-600">
              {ICONS.LOCK}
            </div>
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            Análise Preditiva Indisponível
          </h3>
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
            <p className="text-sm text-blue-700">
              Faça upgrade para o plano Profissional para usar análise preditiva com IA.
            </p>
          </div>
          <Button variant="primary">
            Ver Planos
          </Button>
        </div>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card className="p-6 shadow-lg border-0 bg-gradient-to-br from-green-50 to-emerald-50">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 text-green-600 bg-white rounded-xl p-2 shadow-md">
              {ICONS.BRAIN}
            </div>
            <div>
              <h2 className="text-2xl font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
                Análise Preditiva
              </h2>
              <p className="text-sm text-gray-600">
                Insights baseados em dados históricos e tendências
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-4">
            {usage && usage.limit > 0 && (
              <div className="bg-white/70 backdrop-blur-sm rounded-full px-4 py-2 border border-white/20">
                <div className="text-sm text-gray-700 font-medium">
                  {usage.used} / {usage.limit} análises este mês
                </div>
              </div>
            )}
            <Button
              onClick={handleRefreshAnalysis}
              disabled={isAnalyzing}
              isLoading={isAnalyzing}
              loadingText="Analisando..."
              size="sm"
              className="bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
            >
              <div className="w-4 h-4 mr-2">
                {ICONS.REFRESH_CW}
              </div>
              Atualizar Análise
            </Button>
          </div>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-4">
            <p className="text-sm text-red-700">{error}</p>
          </div>
        )}

        {/* Categorias */}
        <div className="border-b border-gray-200 mb-6">
          <nav className="-mb-px flex space-x-8">
            {categories.map((category) => (
              <button
                type="button"
                key={category.key}
                onClick={() => setSelectedCategory(category.key as keyof PredictiveData)}
                className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                  selectedCategory === category.key
                    ? 'border-green-500 text-green-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="w-4 h-4">
                  {category.icon}
                </div>
                <span>{category.name}</span>
              </button>
            ))}
          </nav>
        </div>

        {/* Insights */}
        {insights && insights[selectedCategory] && (
          <div className="space-y-4">
            {insights[selectedCategory].map((insight) => (
              <div key={insight.id} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-start space-x-3">
                    {getInsightIcon(insight.type)}
                    <div>
                      <h3 className="font-medium text-gray-900">{insight.title}</h3>
                      <p className="text-sm text-gray-600 mt-1">{insight.description}</p>
                    </div>
                  </div>
                  <div className={`px-2 py-1 rounded text-xs border ${getImpactColor(insight.impact)}`}>
                    {getImpactLabel(insight.impact)}
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                  <div className="bg-gray-50 rounded-lg p-3">
                    <div className="text-xs text-gray-500 mb-1">Atual</div>
                    <div className="text-lg font-semibold text-gray-900">
                      {insight.data.current}
                    </div>
                  </div>
                  <div className="bg-purple-50 rounded-lg p-3">
                    <div className="text-xs text-purple-600 mb-1">Previsto</div>
                    <div className="text-lg font-semibold text-purple-900">
                      {insight.data.predicted}
                    </div>
                  </div>
                  <div className={`rounded-lg p-3 ${
                    insight.data.change > 0 ? 'bg-red-50' : 'bg-green-50'
                  }`}>
                    <div className={`text-xs mb-1 ${
                      insight.data.change > 0 ? 'text-red-600' : 'text-green-600'
                    }`}>
                      Variação
                    </div>
                    <div className={`text-lg font-semibold ${
                      insight.data.change > 0 ? 'text-red-900' : 'text-green-900'
                    }`}>
                      {insight.data.change > 0 ? '+' : ''}{insight.data.change}%
                    </div>
                  </div>
                </div>

                <div className="flex items-center justify-between text-sm">
                  <div className="flex items-center space-x-4">
                    <span className="text-gray-500">
                      Confiança: {(insight.confidence * 100).toFixed(0)}%
                    </span>
                    <span className="text-gray-500">
                      {insight.timeframe}
                    </span>
                  </div>
                </div>

                {insight.actionItems.length > 0 && (
                  <div className="mt-4 pt-4 border-t border-gray-200">
                    <h4 className="text-sm font-medium text-gray-900 mb-2">
                      Ações Recomendadas:
                    </h4>
                    <ul className="space-y-1">
                      {insight.actionItems.map((action, index) => (
                        <li key={index} className="text-sm text-gray-600 flex items-start">
                          <span className="text-purple-500 mr-2">•</span>
                          {action}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}

        {isAnalyzing && (
          <div className="text-center py-8">
            <div className="bg-purple-100 rounded-full p-4 inline-block mb-4">
              <div className="w-8 h-8 text-purple-600 animate-spin">
                {ICONS.REFRESH_CW}
              </div>
            </div>
            <p className="text-gray-600">Gerando insights preditivos...</p>
          </div>
        )}
      </Card>
    </div>
  );
};

export default PredictiveAnalytics;
