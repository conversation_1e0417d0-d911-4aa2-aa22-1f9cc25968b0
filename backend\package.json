{"name": "plans-admin-backend", "version": "1.0.0", "description": "Backend e dashboard administrativo para gerenciamento de planos", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "build": "cd dashboard && npm run build", "dev": "nodemon server.js", "install-all": "npm install && cd dashboard && npm install", "setup": "npm run install-all && npm run build", "dashboard:dev": "cd dashboard && npm run dev", "dashboard:build": "cd dashboard && npm run build", "dashboard:preview": "cd dashboard && npm run preview", "cli": "node cli.js", "init": "node cli.js init", "reset": "node cli.js reset", "create-user": "node cli.js create-user", "list-users": "node cli.js list-users", "system-info": "node cli.js system-info", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:integration": "jest --testPathPattern=integration", "test:unit": "jest --testPathPattern=unit", "test:api": "jest --testPathPattern=api"}, "bin": {"promandato": "./cli.js"}, "dependencies": {"bcryptjs": "^3.0.2", "body-parser": "^1.20.2", "commander": "^14.0.0", "cors": "^2.8.5", "csv-parser": "^3.2.0", "dotenv": "^16.5.0", "express": "^4.18.2", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "fs-extra": "^11.1.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.513.0", "multer": "^2.0.1", "nodemailer": "^7.0.3", "socket.io": "^4.8.1", "stripe": "^18.2.1", "uuid": "^11.1.0"}, "devDependencies": {"@babel/core": "^7.25.9", "@babel/plugin-syntax-import-meta": "^7.10.4", "@babel/preset-env": "^7.25.9", "babel-jest": "^29.7.0", "jest": "^29.7.0", "nodemon": "^3.0.1", "supertest": "^7.0.0", "vite": "^6.3.5"}, "keywords": ["admin", "dashboard", "plans", "pricing", "management"], "author": "Sistema de Gestão Política", "license": "MIT"}