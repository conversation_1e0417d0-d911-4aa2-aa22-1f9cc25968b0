import { useState, useEffect, useCallback, useRef } from 'react';
import { notificationService, Notification, NotificationFilters } from '../services/notificationService';
import websocketService from '../services/websocketService';
import { useAuth } from './useAuth';

interface UseNotificationsReturn {
  notifications: Notification[];
  unreadCount: number;
  loading: boolean;
  error: string | null;
  
  // Actions
  fetchNotifications: (filters?: NotificationFilters) => Promise<void>;
  markAsRead: (id: string) => Promise<void>;
  markAsUnread: (id: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  deleteNotification: (id: string) => Promise<void>;
  refreshUnreadCount: () => Promise<void>;
  
  // Filters
  getUnreadNotifications: () => Notification[];
  getNotificationsByCategory: (category: string) => Notification[];
  getNotificationsByType: (type: string) => Notification[];
}

interface UseNotificationsOptions {
  autoRefresh?: boolean;
  refreshInterval?: number;
  initialFilters?: NotificationFilters;
  enablePolling?: boolean;
  enableWebSocket?: boolean;
  maxRetries?: number;
}

export const useNotifications = (options: UseNotificationsOptions = {}): UseNotificationsReturn => {
  const {
    autoRefresh = true,
    refreshInterval = 300000, // 5 minutos (muito reduzido, pois WebSocket é prioritário)
    initialFilters,
    enablePolling = false, // Desabilitado por padrão, WebSocket é prioritário
    enableWebSocket = true, // Habilitado por padrão
    maxRetries = 2
  } = options;

  const { currentUser } = useAuth();

  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const lastCheckTimeRef = useRef<string>(new Date().toISOString());
  const retryCountRef = useRef<number>(0);
  const isTabActiveRef = useRef<boolean>(true);
  const lastActivityRef = useRef<number>(Date.now());
  const websocketConnectedRef = useRef<boolean>(false);
  const cleanupFunctionsRef = useRef<Array<() => void>>([]);

  // Buscar notificações
  const fetchNotifications = useCallback(async (filters?: NotificationFilters) => {
    try {
      setLoading(true);
      setError(null);

      const response = await notificationService.getNotifications(filters || initialFilters);
      
      if (response.success && response.data) {
        setNotifications(response.data);
      } else {
        setError(response.error || 'Erro ao buscar notificações');
      }
    } catch (err) {
      setError('Erro inesperado ao buscar notificações');
      console.error('Erro ao buscar notificações:', err);
    } finally {
      setLoading(false);
    }
  }, [initialFilters]);

  // Atualizar contagem de não lidas
  const refreshUnreadCount = useCallback(async () => {
    try {
      const response = await notificationService.getUnreadCount();
      
      if (response.success && response.data) {
        setUnreadCount(response.data.count);
      }
    } catch (err) {
      console.error('Erro ao buscar contagem de não lidas:', err);
    }
  }, []);

  // Marcar como lida
  const markAsRead = useCallback(async (id: string) => {
    try {
      const response = await notificationService.markAsRead(id);

      if (response.success) {
        // Atualizar estado local
        setNotifications(prev =>
          prev.map(notification =>
            notification.id === id
              ? { ...notification, read: true, readAt: new Date().toISOString() }
              : notification
          )
        );

        // Atualizar contagem
        setUnreadCount(prev => Math.max(0, prev - 1));

        // Notificar via WebSocket para sincronizar outros dispositivos
        if (websocketConnectedRef.current) {
          websocketService.markNotificationAsRead(id);
        }
      } else {
        setError(response.error || 'Erro ao marcar como lida');
      }
    } catch (err) {
      setError('Erro inesperado ao marcar como lida');
      console.error('Erro ao marcar como lida:', err);
    }
  }, []);

  // Marcar como não lida
  const markAsUnread = useCallback(async (id: string) => {
    try {
      const response = await notificationService.markAsUnread(id);
      
      if (response.success) {
        // Atualizar estado local
        setNotifications(prev => 
          prev.map(notification => 
            notification.id === id 
              ? { ...notification, read: false, readAt: undefined }
              : notification
          )
        );
        
        // Atualizar contagem
        setUnreadCount(prev => prev + 1);
      } else {
        setError(response.error || 'Erro ao marcar como não lida');
      }
    } catch (err) {
      setError('Erro inesperado ao marcar como não lida');
      console.error('Erro ao marcar como não lida:', err);
    }
  }, []);

  // Marcar todas como lidas
  const markAllAsRead = useCallback(async () => {
    try {
      const response = await notificationService.markAllAsRead();
      
      if (response.success) {
        // Atualizar estado local
        const now = new Date().toISOString();
        setNotifications(prev => 
          prev.map(notification => ({
            ...notification,
            read: true,
            readAt: now
          }))
        );
        
        // Zerar contagem
        setUnreadCount(0);
      } else {
        setError(response.error || 'Erro ao marcar todas como lidas');
      }
    } catch (err) {
      setError('Erro inesperado ao marcar todas como lidas');
      console.error('Erro ao marcar todas como lidas:', err);
    }
  }, []);

  // Deletar notificação
  const deleteNotification = useCallback(async (id: string) => {
    try {
      const response = await notificationService.deleteNotification(id);
      
      if (response.success) {
        // Remover do estado local
        const notificationToDelete = notifications.find(n => n.id === id);
        setNotifications(prev => prev.filter(notification => notification.id !== id));
        
        // Atualizar contagem se era não lida
        if (notificationToDelete && !notificationToDelete.read) {
          setUnreadCount(prev => Math.max(0, prev - 1));
        }
      } else {
        setError(response.error || 'Erro ao deletar notificação');
      }
    } catch (err) {
      setError('Erro inesperado ao deletar notificação');
      console.error('Erro ao deletar notificação:', err);
    }
  }, [notifications]);

  // Filtros locais
  const getUnreadNotifications = useCallback(() => {
    return notifications.filter(notification => !notification.read);
  }, [notifications]);

  const getNotificationsByCategory = useCallback((category: string) => {
    return notifications.filter(notification => notification.category === category);
  }, [notifications]);

  const getNotificationsByType = useCallback((type: string) => {
    return notifications.filter(notification => notification.type === type);
  }, [notifications]);

  // Verificar novas notificações (polling otimizado)
  const checkForNewNotifications = useCallback(async () => {
    // Não fazer polling se a aba não estiver ativa
    if (!isTabActiveRef.current) {
      console.log('Tab não ativa, pulando verificação de notificações');
      return;
    }

    // Implementar backoff exponencial em caso de erros
    const backoffDelay = Math.min(1000 * Math.pow(2, retryCountRef.current), 30000);

    try {
      const response = await notificationService.checkForNewNotifications(lastCheckTimeRef.current);

      if (response.success && response.data && response.data.length > 0) {
        // Adicionar novas notificações ao início da lista
        setNotifications(prev => [...response.data!, ...prev]);

        // Atualizar contagem de não lidas
        const newUnreadCount = response.data.filter(n => !n.read).length;
        setUnreadCount(prev => prev + newUnreadCount);

        // Atualizar timestamp da última verificação
        lastCheckTimeRef.current = new Date().toISOString();

        // Reset retry count em caso de sucesso
        retryCountRef.current = 0;
      } else {
        // Reset retry count se não há novas notificações (sucesso)
        retryCountRef.current = 0;
      }
    } catch (err) {
      console.error('Erro ao verificar novas notificações:', err);
      retryCountRef.current = Math.min(retryCountRef.current + 1, maxRetries);

      // Se atingiu o máximo de tentativas, aguardar mais tempo
      if (retryCountRef.current >= maxRetries) {
        console.log(`Máximo de tentativas atingido (${maxRetries}), aguardando ${backoffDelay}ms`);
        setTimeout(() => {
          retryCountRef.current = 0; // Reset após o delay
        }, backoffDelay);
      }
    }
  }, [maxRetries]);

  // Detectar se a aba está ativa
  useEffect(() => {
    const handleVisibilityChange = () => {
      isTabActiveRef.current = !document.hidden;
      lastActivityRef.current = Date.now();

      if (isTabActiveRef.current) {
        console.log('Tab ativa, retomando verificações de notificações');
        // Verificar imediatamente quando a aba volta a ficar ativa
        checkForNewNotifications();
        refreshUnreadCount();
      } else {
        console.log('Tab inativa, pausando verificações de notificações');
      }
    };

    const handleUserActivity = () => {
      lastActivityRef.current = Date.now();
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    document.addEventListener('mousedown', handleUserActivity);
    document.addEventListener('keydown', handleUserActivity);
    document.addEventListener('scroll', handleUserActivity);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      document.removeEventListener('mousedown', handleUserActivity);
      document.removeEventListener('keydown', handleUserActivity);
      document.removeEventListener('scroll', handleUserActivity);
    };
  }, [checkForNewNotifications, refreshUnreadCount]);

  // Configurar polling automático otimizado
  useEffect(() => {
    if (autoRefresh && enablePolling) {
      const startPolling = () => {
        intervalRef.current = setInterval(() => {
          // Verificar se o usuário está ativo (atividade nos últimos 5 minutos)
          const timeSinceActivity = Date.now() - lastActivityRef.current;
          const isUserActive = timeSinceActivity < 5 * 60 * 1000; // 5 minutos

          if (isTabActiveRef.current && isUserActive) {
            checkForNewNotifications();
            refreshUnreadCount();
          } else {
            console.log('Usuário inativo, pulando verificação de notificações');
          }
        }, refreshInterval);
      };

      startPolling();

      return () => {
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
        }
      };
    }
  }, [autoRefresh, enablePolling, refreshInterval, checkForNewNotifications, refreshUnreadCount]);

  // Configurar WebSocket
  useEffect(() => {
    if (enableWebSocket && currentUser?.id) {
      const connectWebSocket = async () => {
        try {
          console.log('🔌 Conectando WebSocket para notificações...');
          await websocketService.connect(currentUser.id);
          websocketConnectedRef.current = true;
          console.log('✅ WebSocket conectado para notificações');

          // Configurar listeners
          const unsubscribeNewNotification = websocketService.onNewNotification((notification) => {
            console.log('📨 Nova notificação recebida via WebSocket:', notification);
            setNotifications(prev => [notification, ...prev]);
            if (!notification.read) {
              setUnreadCount(prev => prev + 1);
            }
          });

          const unsubscribeUnreadCount = websocketService.onUnreadCountUpdate((count) => {
            console.log('🔢 Contagem atualizada via WebSocket:', count);
            setUnreadCount(count);
          });

          const unsubscribeNotificationRead = websocketService.onNotificationRead((notificationId) => {
            console.log('👁️ Notificação marcada como lida via WebSocket:', notificationId);
            setNotifications(prev =>
              prev.map(notification =>
                notification.id === notificationId
                  ? { ...notification, read: true, readAt: new Date().toISOString() }
                  : notification
              )
            );
            setUnreadCount(prev => Math.max(0, prev - 1));
          });

          const unsubscribeRefreshCount = websocketService.onRefreshUnreadCount(() => {
            console.log('🔄 Atualizando contagem via WebSocket');
            refreshUnreadCount();
          });

          // Armazenar funções de cleanup
          cleanupFunctionsRef.current = [
            unsubscribeNewNotification,
            unsubscribeUnreadCount,
            unsubscribeNotificationRead,
            unsubscribeRefreshCount
          ];

        } catch (error) {
          console.error('❌ Erro ao conectar WebSocket:', error);
          websocketConnectedRef.current = false;

          // Fallback para polling se WebSocket falhar
          if (!enablePolling) {
            console.log('🔄 Habilitando polling como fallback');
            // Força habilitar polling temporariamente
          }
        }
      };

      connectWebSocket();

      return () => {
        // Cleanup WebSocket
        cleanupFunctionsRef.current.forEach(cleanup => cleanup());
        cleanupFunctionsRef.current = [];
        websocketService.disconnect();
        websocketConnectedRef.current = false;
        console.log('🔌 WebSocket desconectado');
      };
    }
  }, [enableWebSocket, currentUser?.id, refreshUnreadCount]);

  // Buscar dados iniciais
  useEffect(() => {
    fetchNotifications();
    refreshUnreadCount();
  }, [fetchNotifications, refreshUnreadCount]);

  // Limpar erro após 5 segundos
  useEffect(() => {
    if (error) {
      const timer = setTimeout(() => {
        setError(null);
      }, 5000);

      return () => clearTimeout(timer);
    }
  }, [error]);

  return {
    notifications,
    unreadCount,
    loading,
    error,
    
    // Actions
    fetchNotifications,
    markAsRead,
    markAsUnread,
    markAllAsRead,
    deleteNotification,
    refreshUnreadCount,
    
    // Filters
    getUnreadNotifications,
    getNotificationsByCategory,
    getNotificationsByType
  };
};
