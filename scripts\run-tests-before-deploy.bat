@echo off
echo ========================================
echo    EXECUTANDO TESTES ANTES DO DEPLOY
echo ========================================
echo.

echo Este script executa todos os testes para garantir
echo que o codigo esta pronto para deploy.
echo.

echo [1/5] Verificando se todas as ferramentas estao instaladas...

set MISSING_TOOLS=0

if not exist "backend\node_modules\jest" (
    echo ❌ Jest (Backend) nao encontrado
    set MISSING_TOOLS=1
)

if not exist "frontend\node_modules\vitest" (
    echo ❌ Vitest (Frontend) nao encontrado
    set MISSING_TOOLS=1
)

if not exist "node_modules\@playwright\test" (
    echo ❌ Playwright (E2E) nao encontrado
    set MISSING_TOOLS=1
)

if %MISSING_TOOLS% equ 1 (
    echo.
    echo ERRO: Algumas ferramentas de teste nao estao instaladas!
    echo Execute primeiro: scripts\setup-tests.bat
    pause
    exit /b 1
)

echo ✅ Todas as ferramentas de teste estao instaladas!

echo.
echo [2/5] Executando testes do Backend...
cd backend
npx jest --silent
set BACKEND_RESULT=%errorlevel%
cd ..

if %BACKEND_RESULT% equ 0 (
    echo ✅ Backend: PASSOU
) else (
    echo ❌ Backend: FALHOU
)

echo.
echo [3/5] Executando testes do Frontend...
cd frontend
npm run test:run -- --reporter=basic
set FRONTEND_RESULT=%errorlevel%
cd ..

if %FRONTEND_RESULT% equ 0 (
    echo ✅ Frontend: PASSOU
) else (
    echo ❌ Frontend: FALHOU
)

echo.
echo [4/5] Verificando se servicos estao rodando para E2E...
echo (Pulando testes E2E por enquanto - requer servicos rodando)
set E2E_RESULT=0

echo.
echo [5/5] Resumo dos resultados...

echo.
echo ========================================
echo           RESULTADOS DOS TESTES
echo ========================================
echo.

if %BACKEND_RESULT% equ 0 (
    echo ✅ Backend: PASSOU
) else (
    echo ❌ Backend: FALHOU
)

if %FRONTEND_RESULT% equ 0 (
    echo ✅ Frontend: PASSOU
) else (
    echo ❌ Frontend: FALHOU
)

if %E2E_RESULT% equ 0 (
    echo ⏭️  E2E: PULADO (requer servicos rodando)
) else (
    echo ❌ E2E: FALHOU
)

echo.
set /a TOTAL_FAILURES=%BACKEND_RESULT%+%FRONTEND_RESULT%+%E2E_RESULT%

if %TOTAL_FAILURES% equ 0 (
    echo ========================================
    echo      🎉 TODOS OS TESTES PASSARAM!
    echo         PRONTO PARA DEPLOY!
    echo ========================================
    echo.
    echo Voce pode agora executar:
    echo - scripts\fix-cors-deploy.bat (corrigir CORS e fazer deploy)
    echo - scripts\deploy-backend-fixed.bat (deploy do backend)
    echo.
) else (
    echo ========================================
    echo       ❌ ALGUNS TESTES FALHARAM!
    echo        NAO FAZER DEPLOY AINDA!
    echo ========================================
    echo.
    echo Corrija os problemas antes de fazer deploy.
    echo.
    echo Para debug:
    echo - Backend: cd backend ^&^& npm run test
    echo - Frontend: cd frontend ^&^& npm run test
    echo.
)

pause
exit /b %TOTAL_FAILURES%
