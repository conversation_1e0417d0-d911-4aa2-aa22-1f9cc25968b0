#!/bin/bash

echo "========================================"
echo "   DEBUG: SOCIAL MEDIA + PERFIL"
echo "========================================"
echo

cd frontend

echo "[1/2] Iniciando servidor com debug..."
echo
echo "========================================"
echo "      🔍 MODO DEBUG ATIVADO"
echo "========================================"
echo
echo "PASSOS PARA DEBUG:"
echo
echo "1. VERIFIQUE SEU PERFIL:"
echo "   - Acesse: http://localhost:5173/politician-profile"
echo "   - Confirme que os dados de redes sociais estão salvos"
echo "   - Anote exatamente como preencheu os campos"
echo
echo "2. TESTE A SOCIAL MEDIA:"
echo "   - Acesse: http://localhost:5173/social-media"
echo "   - Clique no botão '🔄 Recarregar Dados'"
echo "   - Abra o Console do navegador (F12)"
echo "   - Procure por logs que começam com '🔍 Debug'"
echo
echo "3. ANALISE OS LOGS:"
echo "   - Profile: deve mostrar seus dados"
echo "   - Profile.socialMedia: deve mostrar as redes sociais"
echo "   - Accounts: deve mostrar as contas criadas"
echo
echo "4. SE NÃO APARECER NADA:"
echo "   - Volte para /politician-profile"
echo "   - Preencha novamente os campos"
echo "   - Salve"
echo "   - Volte para /social-media"
echo "   - Clique '🔄 Recarregar Dados'"
echo
echo "========================================"
echo "      📋 FORMATO CORRETO DOS DADOS"
echo "========================================"
echo
echo "Instagram: @seuusuario (com ou sem @)"
echo "Facebook: suapagina ou https://facebook.com/suapagina"
echo "Twitter: @seuusuario (com ou sem @)"
echo "TikTok: @seuusuario (com ou sem @)"
echo
echo "========================================"
echo "      🚀 SERVIDOR INICIANDO..."
echo "========================================"
echo
echo "Pressione Ctrl+C para parar o servidor"
echo

npm run dev

echo
cd ..
read -p "Pressione Enter para continuar..."
