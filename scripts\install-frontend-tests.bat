@echo off
echo ========================================
echo   INSTALANDO DEPENDENCIAS DE TESTE - FRONTEND
echo ========================================
echo.

echo [1/3] Navegando para o diretorio frontend...
cd frontend

echo.
echo [2/3] Instalando dependencias de teste...
npm install --save-dev vitest@^2.1.8 @vitest/ui@^2.1.8 @testing-library/react@^16.1.0 @testing-library/jest-dom@^6.6.3 @testing-library/user-event@^14.5.2 jsdom@^26.0.0 @vitest/coverage-v8@^2.1.8

if %errorlevel% neq 0 (
    echo ERRO: Falha ao instalar dependencias de teste
    pause
    exit /b 1
)

echo.
echo [3/3] Verificando instalacao...
npx vitest --version

if %errorlevel% neq 0 (
    echo ERRO: Vitest nao foi instalado corretamente
    pause
    exit /b 1
)

echo.
echo ========================================
echo   DEPENDENCIAS INSTALADAS COM SUCESSO!
echo ========================================
echo.
echo Agora voce pode executar:
echo - npm run test
echo - npm run test:ui
echo - npm run test:coverage
echo.

cd ..
pause
