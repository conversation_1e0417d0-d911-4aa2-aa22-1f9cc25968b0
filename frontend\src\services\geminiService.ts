import { getAISettings, updateAIUsage } from './firebaseService';

interface GeminiResponse {
  candidates: Array<{
    content: {
      parts: Array<{
        text: string;
      }>;
    };
  }>;
}

interface SentimentResult {
  sentiment: 'positive' | 'negative' | 'neutral';
  confidence: number;
  explanation: string;
}

interface CategoryResult {
  category: string;
  confidence: number;
  subcategory?: string;
  explanation: string;
}

interface SmartResponseResult {
  response: string;
  tone: 'formal' | 'friendly' | 'professional';
  confidence: number;
}

class GeminiService {
  private apiKey: string | null = null;
  private baseUrl = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent';

  async initialize(): Promise<boolean> {
    try {
      const settings = await getAISettings();
      if (settings?.geminiApiKey && settings.preferredProvider === 'gemini') {
        this.apiKey = settings.geminiApiKey;
        return true;
      }
      return false;
    } catch (error) {
      console.error('Erro ao inicializar Gemini:', error);
      return false;
    }
  }

  private async makeRequest(prompt: string): Promise<string> {
    if (!this.apiKey) {
      throw new Error('Gemini API não configurada. Configure sua chave API nas configurações.');
    }

    try {
      const response = await fetch(`${this.baseUrl}?key=${this.apiKey}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [{
            parts: [{
              text: prompt
            }]
          }]
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`Erro da API Gemini: ${errorData.error?.message || response.statusText}`);
      }

      const data: GeminiResponse = await response.json();
      
      if (!data.candidates || data.candidates.length === 0) {
        throw new Error('Nenhuma resposta gerada pela API');
      }

      // Incrementar uso da API
      await updateAIUsage(1);

      return data.candidates[0].content.parts[0].text;
    } catch (error) {
      console.error('Erro na requisição Gemini:', error);
      throw error;
    }
  }

  async analyzeSentiment(text: string): Promise<SentimentResult> {
    const prompt = `
Analise o sentimento do seguinte texto em português brasileiro e retorne APENAS um JSON válido no formato:
{
  "sentiment": "positive|negative|neutral",
  "confidence": 0.85,
  "explanation": "Explicação breve do sentimento identificado"
}

Texto para análise: "${text}"

Responda APENAS com o JSON, sem texto adicional.
`;

    try {
      const response = await this.makeRequest(prompt);
      const cleanResponse = response.trim().replace(/```json\n?|\n?```/g, '');
      return JSON.parse(cleanResponse);
    } catch (error) {
      console.error('Erro na análise de sentimento:', error);
      // Fallback para análise simples
      return this.fallbackSentimentAnalysis(text);
    }
  }

  async categorizeText(text: string): Promise<CategoryResult> {
    const prompt = `
Categorize o seguinte texto de demanda política em português brasileiro e retorne APENAS um JSON válido no formato:
{
  "category": "Infraestrutura|Saúde|Educação|Segurança|Meio Ambiente|Transporte|Habitação|Assistência Social|Cultura|Esporte|Outros",
  "confidence": 0.85,
  "subcategory": "subcategoria específica (opcional)",
  "explanation": "Explicação breve da categorização"
}

Texto para categorizar: "${text}"

Responda APENAS com o JSON, sem texto adicional.
`;

    try {
      const response = await this.makeRequest(prompt);
      const cleanResponse = response.trim().replace(/```json\n?|\n?```/g, '');
      return JSON.parse(cleanResponse);
    } catch (error) {
      console.error('Erro na categorização:', error);
      // Fallback para categorização simples
      return this.fallbackCategorization(text);
    }
  }

  async generateSmartResponse(context: string, tone: 'formal' | 'friendly' | 'professional' = 'professional'): Promise<SmartResponseResult> {
    const toneInstructions = {
      formal: 'Use linguagem formal e respeitosa, adequada para comunicação oficial.',
      friendly: 'Use linguagem amigável e acessível, mantendo o respeito.',
      professional: 'Use linguagem profissional, clara e objetiva.'
    };

    const prompt = `
Gere uma resposta inteligente para o seguinte contexto em português brasileiro e retorne APENAS um JSON válido no formato:
{
  "response": "Resposta gerada de forma natural e adequada",
  "tone": "${tone}",
  "confidence": 0.85
}

Instruções de tom: ${toneInstructions[tone]}

Contexto: "${context}"

A resposta deve ser útil, empática e adequada para um político se comunicar com cidadãos.
Responda APENAS com o JSON, sem texto adicional.
`;

    try {
      const response = await this.makeRequest(prompt);
      const cleanResponse = response.trim().replace(/```json\n?|\n?```/g, '');
      return JSON.parse(cleanResponse);
    } catch (error) {
      console.error('Erro na geração de resposta:', error);
      // Fallback para resposta simples
      return this.fallbackSmartResponse(context, tone);
    }
  }

  // Métodos de fallback para quando a API falha
  private fallbackSentimentAnalysis(text: string): SentimentResult {
    const positiveWords = ['bom', 'ótimo', 'excelente', 'parabéns', 'obrigado', 'satisfeito', 'feliz'];
    const negativeWords = ['ruim', 'péssimo', 'problema', 'reclamação', 'insatisfeito', 'raiva'];
    
    const lowerText = text.toLowerCase();
    const positiveCount = positiveWords.filter(word => lowerText.includes(word)).length;
    const negativeCount = negativeWords.filter(word => lowerText.includes(word)).length;
    
    if (positiveCount > negativeCount) {
      return {
        sentiment: 'positive',
        confidence: 0.7,
        explanation: 'Análise baseada em palavras-chave positivas identificadas'
      };
    } else if (negativeCount > positiveCount) {
      return {
        sentiment: 'negative',
        confidence: 0.7,
        explanation: 'Análise baseada em palavras-chave negativas identificadas'
      };
    } else {
      return {
        sentiment: 'neutral',
        confidence: 0.6,
        explanation: 'Sentimento neutro - não foram identificadas palavras-chave significativas'
      };
    }
  }

  private fallbackCategorization(text: string): CategoryResult {
    const categories = {
      'Infraestrutura': ['rua', 'asfalto', 'buraco', 'iluminação', 'água', 'esgoto', 'calçada'],
      'Saúde': ['hospital', 'médico', 'remédio', 'consulta', 'posto de saúde', 'sus'],
      'Educação': ['escola', 'professor', 'ensino', 'educação', 'creche', 'universidade'],
      'Segurança': ['polícia', 'violência', 'roubo', 'segurança', 'crime', 'delegacia'],
      'Transporte': ['ônibus', 'transporte', 'trânsito', 'semáforo', 'estrada', 'rodovia'],
      'Meio Ambiente': ['lixo', 'poluição', 'árvore', 'parque', 'meio ambiente', 'reciclagem']
    };

    const lowerText = text.toLowerCase();
    let bestCategory = 'Outros';
    let maxMatches = 0;

    for (const [category, keywords] of Object.entries(categories)) {
      const matches = keywords.filter(keyword => lowerText.includes(keyword)).length;
      if (matches > maxMatches) {
        maxMatches = matches;
        bestCategory = category;
      }
    }

    return {
      category: bestCategory,
      confidence: maxMatches > 0 ? 0.7 : 0.3,
      explanation: maxMatches > 0 
        ? `Categorizado baseado em palavras-chave relacionadas a ${bestCategory}`
        : 'Categoria padrão - não foram identificadas palavras-chave específicas'
    };
  }

  private fallbackSmartResponse(context: string, tone: 'formal' | 'friendly' | 'professional'): SmartResponseResult {
    const responses = {
      formal: 'Agradecemos seu contato. Sua solicitação será analisada e encaminhada aos setores competentes para as devidas providências.',
      friendly: 'Olá! Obrigado por entrar em contato. Vamos analisar sua solicitação e retornar com uma resposta em breve.',
      professional: 'Recebemos sua solicitação e ela será avaliada pela nossa equipe. Retornaremos com informações assim que possível.'
    };

    return {
      response: responses[tone],
      tone,
      confidence: 0.5
    };
  }
}

export const geminiService = new GeminiService();
