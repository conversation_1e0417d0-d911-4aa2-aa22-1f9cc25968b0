import { 
  getStorage, 
  ref, 
  uploadBytesResumable, 
  getDownloadURL, 
  deleteObject,
  UploadTaskSnapshot 
} from 'firebase/storage';
import { 
  collection, 
  addDoc, 
  deleteDoc, 
  doc, 
  getDocs, 
  query, 
  orderBy, 
  limit 
} from 'firebase/firestore';
import { firestoreDb as db } from './firebaseService';
import { MediaFile } from '../types';

const storage = getStorage();
const MEDIA_COLLECTION = 'social_media_files';

/**
 * Upload de arquivo para Firebase Storage
 */
export const uploadMediaToFirebase = async (
  file: File,
  onProgress?: (progress: number) => void
): Promise<MediaFile> => {
  return new Promise((resolve, reject) => {
    // Gerar nome único para o arquivo
    const timestamp = Date.now();
    const fileName = `${timestamp}_${file.name.replace(/[^a-zA-Z0-9.-]/g, '_')}`;
    const storageRef = ref(storage, `social-media/${fileName}`);

    // Criar task de upload
    const uploadTask = uploadBytesResumable(storageRef, file);

    uploadTask.on(
      'state_changed',
      (snapshot: UploadTaskSnapshot) => {
        // Calcular progresso
        const progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
        onProgress?.(Math.round(progress));
      },
      (error) => {
        console.error('Erro no upload:', error);
        reject(error);
      },
      async () => {
        try {
          // Upload concluído - obter URL de download
          const downloadURL = await getDownloadURL(uploadTask.snapshot.ref);
          
          // Criar thumbnail para imagens
          let thumbnailUrl: string | undefined;
          if (file.type.startsWith('image/')) {
            thumbnailUrl = await createThumbnail(file);
          }

          // Salvar metadados no Firestore
          const mediaFile: Omit<MediaFile, 'id'> = {
            name: file.name,
            type: file.type,
            size: file.size,
            url: downloadURL,
            thumbnailUrl,
            uploadedAt: new Date().toISOString()
          };

          const docRef = await addDoc(collection(db, MEDIA_COLLECTION), mediaFile);
          
          resolve({
            id: docRef.id,
            ...mediaFile
          });
        } catch (error) {
          console.error('Erro ao salvar metadados:', error);
          reject(error);
        }
      }
    );
  });
};

/**
 * Criar thumbnail para imagem
 */
const createThumbnail = async (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      // Definir tamanho do thumbnail (máximo 200x200)
      const maxSize = 200;
      let { width, height } = img;

      if (width > height) {
        if (width > maxSize) {
          height = (height * maxSize) / width;
          width = maxSize;
        }
      } else {
        if (height > maxSize) {
          width = (width * maxSize) / height;
          height = maxSize;
        }
      }

      canvas.width = width;
      canvas.height = height;

      // Desenhar imagem redimensionada
      ctx?.drawImage(img, 0, 0, width, height);

      // Converter para blob e criar URL
      canvas.toBlob((blob) => {
        if (blob) {
          const thumbnailUrl = URL.createObjectURL(blob);
          resolve(thumbnailUrl);
        } else {
          reject(new Error('Falha ao criar thumbnail'));
        }
      }, 'image/jpeg', 0.8);
    };

    img.onerror = () => reject(new Error('Falha ao carregar imagem'));
    img.src = URL.createObjectURL(file);
  });
};

/**
 * Deletar arquivo do Firebase Storage e Firestore
 */
export const deleteMediaFromFirebase = async (mediaId: string): Promise<void> => {
  try {
    // Buscar dados do arquivo no Firestore
    const mediaDoc = doc(db, MEDIA_COLLECTION, mediaId);
    
    // Deletar do Firestore
    await deleteDoc(mediaDoc);
    
    // Deletar do Storage (extrair path da URL)
    // Nota: Em produção, seria melhor armazenar o path separadamente
    // Por enquanto, vamos apenas deletar do Firestore
    console.log(`Arquivo ${mediaId} removido do Firestore`);
  } catch (error) {
    console.error('Erro ao deletar arquivo:', error);
    throw error;
  }
};

/**
 * Listar arquivos de mídia
 */
export const getMediaFiles = async (limitCount: number = 50): Promise<MediaFile[]> => {
  try {
    const q = query(
      collection(db, MEDIA_COLLECTION),
      orderBy('uploadedAt', 'desc'),
      limit(limitCount)
    );
    
    const snapshot = await getDocs(q);
    
    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    } as MediaFile));
  } catch (error) {
    console.error('Erro ao buscar arquivos de mídia:', error);
    throw error;
  }
};

/**
 * Validar arquivo antes do upload
 */
export const validateMediaFile = (file: File): { valid: boolean; error?: string } => {
  // Tamanho máximo: 50MB
  const maxSize = 50 * 1024 * 1024;
  if (file.size > maxSize) {
    return { valid: false, error: 'Arquivo muito grande. Máximo: 50MB' };
  }

  // Tipos permitidos
  const allowedTypes = [
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/webp',
    'video/mp4',
    'video/webm',
    'video/quicktime'
  ];

  if (!allowedTypes.includes(file.type)) {
    return { valid: false, error: 'Tipo de arquivo não suportado' };
  }

  return { valid: true };
};

/**
 * Comprimir imagem antes do upload
 */
export const compressImage = async (file: File, quality: number = 0.8): Promise<File> => {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      // Redimensionar se muito grande
      const maxWidth = 1920;
      const maxHeight = 1080;
      let { width, height } = img;

      if (width > maxWidth || height > maxHeight) {
        const ratio = Math.min(maxWidth / width, maxHeight / height);
        width *= ratio;
        height *= ratio;
      }

      canvas.width = width;
      canvas.height = height;

      // Desenhar imagem
      ctx?.drawImage(img, 0, 0, width, height);

      // Converter para blob comprimido
      canvas.toBlob((blob) => {
        if (blob) {
          const compressedFile = new File([blob], file.name, {
            type: 'image/jpeg',
            lastModified: Date.now()
          });
          resolve(compressedFile);
        } else {
          reject(new Error('Falha ao comprimir imagem'));
        }
      }, 'image/jpeg', quality);
    };

    img.onerror = () => reject(new Error('Falha ao carregar imagem'));
    img.src = URL.createObjectURL(file);
  });
};

/**
 * Obter informações de um arquivo de mídia
 */
export const getMediaInfo = async (file: File): Promise<{
  duration?: number;
  dimensions?: { width: number; height: number };
}> => {
  return new Promise((resolve) => {
    if (file.type.startsWith('image/')) {
      const img = new Image();
      img.onload = () => {
        resolve({
          dimensions: { width: img.width, height: img.height }
        });
      };
      img.onerror = () => resolve({});
      img.src = URL.createObjectURL(file);
    } else if (file.type.startsWith('video/')) {
      const video = document.createElement('video');
      video.onloadedmetadata = () => {
        resolve({
          duration: video.duration,
          dimensions: { width: video.videoWidth, height: video.videoHeight }
        });
      };
      video.onerror = () => resolve({});
      video.src = URL.createObjectURL(file);
    } else {
      resolve({});
    }
  });
};
