@echo off
echo ========================================
echo   TESTANDO COMPILACAO DO FRONTEND
echo ========================================
echo.

cd frontend

echo [1/3] Verificando dependencias...
if not exist "node_modules" (
    echo ❌ node_modules nao encontrado!
    echo Execute: npm install
    pause
    exit /b 1
)

echo ✅ node_modules encontrado!

echo.
echo [2/3] Verificando TypeScript...
npx tsc --noEmit

set TS_RESULT=%errorlevel%

echo.
echo [3/3] Resultado:

if %TS_RESULT% equ 0 (
    echo ========================================
    echo      ✅ COMPILACAO TYPESCRIPT OK!
    echo ========================================
    echo.
    echo Nenhum erro de tipo encontrado.
) else (
    echo ========================================
    echo      ❌ ERROS DE TYPESCRIPT ENCONTRADOS!
    echo ========================================
    echo.
    echo Verifique os erros acima e corrija.
)

echo.
cd ..
pause
exit /b %TS_RESULT%
