@echo off
echo ========================================
echo      TESTANDO BACKEND - TESTE SIMPLES
echo ========================================
echo.

echo [1/3] Navegando para o diretorio backend...
cd backend

echo.
echo [2/3] Verificando se Jest esta instalado...
if not exist "node_modules\jest" (
    echo ERRO: Jest nao encontrado!
    echo Execute: scripts\install-backend-tests.bat
    pause
    exit /b 1
)

echo Jest encontrado!

echo.
echo [3/3] Executando teste simples...
npx jest tests/simple.test.js --verbose

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo        TESTE EXECUTADO COM SUCESSO!
    echo ========================================
    echo.
    echo Agora voce pode executar:
    echo - npm run test (todos os testes)
    echo - npm run test:watch (modo watch)
    echo - npm run test:coverage (com cobertura)
) else (
    echo.
    echo ========================================
    echo           TESTE FALHOU!
    echo ========================================
    echo.
    echo Verifique os erros acima.
)

echo.
cd ..
pause
