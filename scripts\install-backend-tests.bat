@echo off
echo ========================================
echo   INSTALANDO DEPENDENCIAS DE TESTE - BACKEND
echo ========================================
echo.

echo [1/3] Navegando para o diretorio backend...
cd backend

echo.
echo [2/3] Instalando dependencias de teste...
npm install --save-dev jest@^29.7.0 supertest@^7.0.0 @babel/core@^7.25.9 @babel/preset-env@^7.25.9 babel-jest@^29.7.0

if %errorlevel% neq 0 (
    echo ERRO: Falha ao instalar dependencias de teste
    pause
    exit /b 1
)

echo.
echo [3/3] Verificando instalacao...
npx jest --version

if %errorlevel% neq 0 (
    echo ERRO: Jest nao foi instalado corretamente
    pause
    exit /b 1
)

echo.
echo ========================================
echo   DEPENDENCIAS INSTALADAS COM SUCESSO!
echo ========================================
echo.
echo Agora voce pode executar:
echo - npm run test
echo - npm run test:watch
echo - npm run test:coverage
echo.

cd ..
pause
