@echo off
echo ========================================
echo     CONFIGURANDO AMBIENTE DE TESTES
echo ========================================
echo.

echo [1/6] Verificando Node.js...
where node >nul 2>&1
if %errorlevel% neq 0 (
    echo ERRO: Node.js nao encontrado!
    echo Instale o Node.js primeiro: https://nodejs.org/
    pause
    exit /b 1
)

node --version
npm --version
echo ✅ Node.js encontrado!
echo.

echo [2/6] Instalando dependencias do projeto raiz...
npm install
if %errorlevel% neq 0 (
    echo ERRO: Falha ao instalar dependencias do projeto raiz
    pause
    exit /b 1
)
echo ✅ Dependencias do projeto raiz instaladas!
echo.

echo [3/6] Instalando dependencias do backend...
cd backend
npm install
if %errorlevel% neq 0 (
    echo ERRO: Falha ao instalar dependencias do backend
    pause
    exit /b 1
)
cd ..
echo ✅ Dependencias do backend instaladas!
echo.

echo [4/6] Instalando dependencias do frontend...
cd frontend
npm install
if %errorlevel% neq 0 (
    echo ERRO: Falha ao instalar dependencias do frontend
    pause
    exit /b 1
)
cd ..
echo ✅ Dependencias do frontend instaladas!
echo.

echo [5/6] Instalando browsers do Playwright...
npx playwright install
if %errorlevel% neq 0 (
    echo ERRO: Falha ao instalar browsers do Playwright
    pause
    exit /b 1
)
echo ✅ Browsers do Playwright instalados!
echo.

echo [6/6] Verificando configuracao...

echo.
echo Verificando Jest (Backend):
cd backend
npx jest --version
cd ..

echo.
echo Verificando Vitest (Frontend):
cd frontend
npx vitest --version
cd ..

echo.
echo Verificando Playwright (E2E):
npx playwright --version

echo.
echo ========================================
echo    CONFIGURACAO CONCLUIDA COM SUCESSO!
echo ========================================
echo.
echo Comandos disponiveis:
echo.
echo === TESTES INDIVIDUAIS ===
echo - Backend:  cd backend ^&^& npm run test
echo - Frontend: cd frontend ^&^& npm run test
echo - E2E:      npm run test:e2e
echo.
echo === TESTES COMBINADOS ===
echo - Todos:           scripts\run-all-tests.bat
echo - Com cobertura:   scripts\run-tests-with-coverage.bat
echo.
echo === TESTES EM MODO WATCH ===
echo - Backend:  cd backend ^&^& npm run test:watch
echo - Frontend: cd frontend ^&^& npm run test:watch
echo.
echo === INTERFACES GRAFICAS ===
echo - Frontend: cd frontend ^&^& npm run test:ui
echo - E2E:      npm run test:e2e:ui
echo.
echo === RELATORIOS ===
echo - E2E Report: npm run test:e2e:report
echo.

echo Pronto para executar testes! 🚀
echo.
pause
