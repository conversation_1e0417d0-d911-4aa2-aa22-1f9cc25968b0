import request from 'supertest';
import express from 'express';
import cors from 'cors';
import bodyParser from 'body-parser';
import fs from 'fs-extra';
import path from 'path';
import analyticsRoutes from '../../routes/analytics.js';

// Configurar app de teste
const app = express();
app.use(cors());
app.use(bodyParser.json());
app.use('/api/analytics', analyticsRoutes);

// Configurar diretório de teste
const TEST_ANALYTICS_DIR = path.join(process.cwd(), 'data', 'analytics', 'test');
const TEST_ANALYTICS_FILE = path.join(TEST_ANALYTICS_DIR, 'landing-page.json');

describe('Analytics API', () => {
  beforeEach(async () => {
    // Garantir que o diretório de teste existe
    await fs.ensureDir(TEST_ANALYTICS_DIR);

    // Limpar arquivo de analytics de teste
    if (await fs.pathExists(TEST_ANALYTICS_FILE)) {
      await fs.remove(TEST_ANALYTICS_FILE);
    }

    // Limpar todo o diretório de analytics para evitar interferência
    const analyticsDir = path.join(process.cwd(), 'data', 'analytics');
    if (await fs.pathExists(analyticsDir)) {
      await fs.emptyDir(analyticsDir);
    }
  });

  afterEach(async () => {
    // Limpar após cada teste
    if (await fs.pathExists(TEST_ANALYTICS_FILE)) {
      await fs.remove(TEST_ANALYTICS_FILE);
    }

    // Limpar todo o diretório de analytics
    const analyticsDir = path.join(process.cwd(), 'data', 'analytics');
    if (await fs.pathExists(analyticsDir)) {
      await fs.emptyDir(analyticsDir);
    }
  });

  describe('POST /api/analytics/landing-page', () => {
    test('should record analytics event successfully', async () => {
      const analyticsData = {
        event: 'page_view',
        sessionId: 'test_session_123',
        url: 'https://example.com/landing',
        referrer: 'https://google.com',
        utm: {
          utm_source: 'google',
          utm_medium: 'cpc',
          utm_campaign: 'test_campaign'
        },
        device: {
          userAgent: 'Mozilla/5.0 Test Browser',
          language: 'pt-BR',
          platform: 'Win32'
        }
      };

      const response = await request(app)
        .post('/api/analytics/landing-page')
        .send(analyticsData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('Analytics recebido');

      // Verificar se os dados foram salvos
      const savedData = await fs.readJson(TEST_ANALYTICS_FILE);
      expect(savedData).toHaveLength(1);
      expect(savedData[0].event).toBe('page_view');
      expect(savedData[0].sessionId).toBe('test_session_123');
      expect(savedData[0].timestamp).toBeDefined();
    });

    test('should add timestamp and IP to analytics data', async () => {
      const analyticsData = {
        event: 'button_click',
        sessionId: 'test_session_456'
      };

      await request(app)
        .post('/api/analytics/landing-page')
        .send(analyticsData)
        .expect(200);

      const savedData = await fs.readJson(TEST_ANALYTICS_FILE);
      expect(savedData[0].timestamp).toBeDefined();
      expect(savedData[0].ip).toBeDefined();
      expect(savedData[0].userAgent).toBeDefined();
    });

    test('should handle multiple analytics events', async () => {
      const events = [
        { event: 'page_view', sessionId: 'session_1' },
        { event: 'scroll_depth', sessionId: 'session_1', percentage: 50 },
        { event: 'button_click', sessionId: 'session_2' }
      ];

      for (const eventData of events) {
        await request(app)
          .post('/api/analytics/landing-page')
          .send(eventData)
          .expect(200);
      }

      const savedData = await fs.readJson(TEST_ANALYTICS_FILE);
      expect(savedData).toHaveLength(3);
      expect(savedData.map(d => d.event)).toEqual(['page_view', 'scroll_depth', 'button_click']);
    });

    test('should handle empty request body', async () => {
      const response = await request(app)
        .post('/api/analytics/landing-page')
        .send({})
        .expect(200);

      expect(response.body.success).toBe(true);

      const savedData = await fs.readJson(TEST_ANALYTICS_FILE);
      expect(savedData).toHaveLength(1);
      expect(savedData[0].timestamp).toBeDefined();
    });

    test('should limit stored events to prevent file bloat', async () => {
      // Simular arquivo com muitos eventos
      const manyEvents = Array.from({ length: 10005 }, (_, i) => ({
        event: 'test_event',
        sessionId: `session_${i}`,
        timestamp: new Date().toISOString()
      }));

      await fs.writeJson(TEST_ANALYTICS_FILE, manyEvents);

      // Adicionar mais um evento
      await request(app)
        .post('/api/analytics/landing-page')
        .send({ event: 'new_event', sessionId: 'new_session' })
        .expect(200);

      const savedData = await fs.readJson(TEST_ANALYTICS_FILE);
      expect(savedData).toHaveLength(10000); // Deve manter apenas 10000 eventos
      expect(savedData[savedData.length - 1].event).toBe('new_event');
    });
  });

  describe('GET /api/analytics/landing-page/stats', () => {
    beforeEach(async () => {
      // Criar dados de teste
      const testEvents = [
        {
          event: 'page_view',
          sessionId: 'session_1',
          timestamp: new Date().toISOString(),
          url: 'https://example.com/page1',
          referrer: 'https://google.com'
        },
        {
          event: 'page_view',
          sessionId: 'session_2',
          timestamp: new Date().toISOString(),
          url: 'https://example.com/page2',
          referrer: 'https://facebook.com'
        },
        {
          event: 'button_click',
          sessionId: 'session_1',
          timestamp: new Date().toISOString()
        },
        {
          event: 'scroll_depth',
          sessionId: 'session_2',
          timestamp: new Date().toISOString(),
          percentage: 75
        }
      ];

      await fs.writeJson(TEST_ANALYTICS_FILE, testEvents);
    });

    test('should return analytics statistics', async () => {
      const response = await request(app)
        .get('/api/analytics/landing-page/stats')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.stats).toBeDefined();

      const stats = response.body.stats;
      expect(stats.totalEvents).toBe(4);
      expect(stats.uniqueSessions).toBe(2);
      expect(stats.eventTypes).toEqual({
        page_view: 2,
        button_click: 1,
        scroll_depth: 1
      });
      expect(stats.topPages).toBeDefined();
      expect(stats.topReferrers).toBeDefined();
    });

    test('should handle empty analytics file', async () => {
      await fs.writeJson(TEST_ANALYTICS_FILE, []);

      const response = await request(app)
        .get('/api/analytics/landing-page/stats')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.stats.totalEvents).toBe(0);
      expect(response.body.stats.uniqueSessions).toBe(0);
    });

    test('should handle missing analytics file', async () => {
      await fs.remove(TEST_ANALYTICS_FILE);

      const response = await request(app)
        .get('/api/analytics/landing-page/stats')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.stats.totalEvents).toBe(0);
    });

    test('should calculate last week statistics', async () => {
      const now = new Date();
      const lastWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      const twoWeeksAgo = new Date(now.getTime() - 14 * 24 * 60 * 60 * 1000);

      const testEvents = [
        {
          event: 'page_view',
          sessionId: 'session_1',
          timestamp: now.toISOString() // Dentro da última semana
        },
        {
          event: 'page_view',
          sessionId: 'session_2',
          timestamp: twoWeeksAgo.toISOString() // Fora da última semana
        }
      ];

      await fs.writeJson(TEST_ANALYTICS_FILE, testEvents);

      const response = await request(app)
        .get('/api/analytics/landing-page/stats')
        .expect(200);

      expect(response.body.stats.lastWeek).toHaveLength(1);
      expect(response.body.stats.lastWeek[0].sessionId).toBe('session_1');
    });
  });

  describe('DELETE /api/analytics/landing-page/cleanup', () => {
    beforeEach(async () => {
      const now = new Date();
      const oldDate = new Date(now.getTime() - 40 * 24 * 60 * 60 * 1000); // 40 dias atrás
      const recentDate = new Date(now.getTime() - 10 * 24 * 60 * 60 * 1000); // 10 dias atrás

      const testEvents = [
        {
          event: 'old_event',
          sessionId: 'old_session',
          timestamp: oldDate.toISOString()
        },
        {
          event: 'recent_event',
          sessionId: 'recent_session',
          timestamp: recentDate.toISOString()
        },
        {
          event: 'current_event',
          sessionId: 'current_session',
          timestamp: now.toISOString()
        }
      ];

      await fs.writeJson(TEST_ANALYTICS_FILE, testEvents);
    });

    test('should cleanup old analytics data', async () => {
      const response = await request(app)
        .delete('/api/analytics/landing-page/cleanup')
        .send({ daysToKeep: 30 })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('limpeza');

      const remainingData = await fs.readJson(TEST_ANALYTICS_FILE);
      expect(remainingData).toHaveLength(2); // Apenas eventos recentes
      expect(remainingData.map(d => d.event)).toEqual(['recent_event', 'current_event']);
    });

    test('should use default cleanup period when not specified', async () => {
      const response = await request(app)
        .delete('/api/analytics/landing-page/cleanup')
        .send({})
        .expect(200);

      expect(response.body.success).toBe(true);
    });

    test('should handle empty analytics file during cleanup', async () => {
      await fs.writeJson(TEST_ANALYTICS_FILE, []);

      const response = await request(app)
        .delete('/api/analytics/landing-page/cleanup')
        .send({ daysToKeep: 30 })
        .expect(200);

      expect(response.body.success).toBe(true);
    });
  });
});
