@echo off
echo ========================================
echo    EXECUTANDO TODOS OS TESTES
echo ========================================
echo.

echo [1/4] Verificando dependencias...
where node >nul 2>&1
if %errorlevel% neq 0 (
    echo ERRO: Node.js nao encontrado!
    pause
    exit /b 1
)

echo Node.js encontrado!
echo.

echo [2/4] Instalando dependencias se necessario...
if not exist "node_modules" (
    echo Instalando dependencias do projeto raiz...
    npm install
)

if not exist "backend\node_modules" (
    echo Instalando dependencias do backend...
    cd backend && npm install && cd ..
)

if not exist "frontend\node_modules" (
    echo Instalando dependencias do frontend...
    cd frontend && npm install && cd ..
)

echo.
echo [3/4] Executando testes...

echo.
echo === TESTES DO BACKEND ===
cd backend
npm run test
set BACKEND_EXIT_CODE=%errorlevel%
cd ..

echo.
echo === TESTES DO FRONTEND ===
cd frontend
npm run test:run
set FRONTEND_EXIT_CODE=%errorlevel%
cd ..

echo.
echo === TESTES E2E ===
npm run test:e2e
set E2E_EXIT_CODE=%errorlevel%

echo.
echo [4/4] Resultados dos testes:
echo.

if %BACKEND_EXIT_CODE% equ 0 (
    echo ✅ Backend: PASSOU
) else (
    echo ❌ Backend: FALHOU
)

if %FRONTEND_EXIT_CODE% equ 0 (
    echo ✅ Frontend: PASSOU
) else (
    echo ❌ Frontend: FALHOU
)

if %E2E_EXIT_CODE% equ 0 (
    echo ✅ E2E: PASSOU
) else (
    echo ❌ E2E: FALHOU
)

echo.
set /a TOTAL_FAILURES=%BACKEND_EXIT_CODE%+%FRONTEND_EXIT_CODE%+%E2E_EXIT_CODE%

if %TOTAL_FAILURES% equ 0 (
    echo ========================================
    echo       TODOS OS TESTES PASSARAM! 🎉
    echo ========================================
) else (
    echo ========================================
    echo      ALGUNS TESTES FALHARAM! ❌
    echo ========================================
    echo.
    echo Verifique os logs acima para detalhes.
)

echo.
echo Relatorios disponiveis:
echo - Backend: backend\coverage\
echo - Frontend: frontend\coverage\
echo - E2E: playwright-report\
echo.

pause
exit /b %TOTAL_FAILURES%
