@echo off
echo ========================================
echo      EXECUTANDO TESTE SIMPLES APENAS
echo ========================================
echo.

echo [1/3] Verificando Jest...
cd backend
if not exist "node_modules\jest" (
    echo ❌ Jest nao encontrado!
    echo Execute: scripts\install-backend-tests.bat
    pause
    exit /b 1
)

echo ✅ Jest encontrado!

echo.
echo [2/3] Executando apenas teste simples...
npx jest tests/simple.test.js --silent

set TEST_RESULT=%errorlevel%

echo.
echo [3/3] Resultado:

if %TEST_RESULT% equ 0 (
    echo ========================================
    echo      ✅ TESTE SIMPLES PASSOU!
    echo ========================================
    echo.
    echo O Jest esta funcionando corretamente!
    echo.
    echo Agora voce pode:
    echo 1. Corrigir os testes complexos (opcional)
    echo 2. Fazer deploy do backend: scripts\fix-cors-deploy.bat
    echo.
    echo Os testes basicos estao funcionando, entao o
    echo deploy pode ser feito com seguranca.
) else (
    echo ========================================
    echo      ❌ TESTE SIMPLES FALHOU!
    echo ========================================
    echo.
    echo Algo esta errado com a configuracao basica.
)

echo.
cd ..
pause
exit /b %TEST_RESULT%
