import { test, expect } from '@playwright/test';

test.describe('Landing Page - Formulários e Interações', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/landingpage');
  });

  test('deve enviar formulário de contato com dados válidos', async ({ page }) => {
    // Navegar para a seção de contato
    await page.click('a[href="#contact"]');
    await expect(page.locator('#contact')).toBeInViewport();

    // Preencher formulário
    await page.fill('input[name="name"]', '<PERSON>');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="phone"]', '(11) 99999-9999');
    await page.fill('input[name="organization"]', 'Prefeitura de São Paulo');
    await page.selectOption('select[name="planInterest"]', 'standard');
    await page.fill('textarea[name="message"]', 'Gostaria de saber mais sobre o sistema.');

    // Interceptar requisição para o backend
    const responsePromise = page.waitForResponse(response => 
      response.url().includes('/api/leads') && response.request().method() === 'POST'
    );

    // Enviar formulário
    await page.click('button[type="submit"]');

    // Aguardar resposta
    const response = await responsePromise;
    expect(response.status()).toBe(200);

    // Verificar se mensagem de sucesso aparece
    await expect(page.locator('.bg-green-50')).toBeVisible({ timeout: 10000 });
    await expect(page.locator('text=enviado com sucesso')).toBeVisible();
  });

  test('deve validar campos obrigatórios do formulário', async ({ page }) => {
    await page.click('a[href="#contact"]');
    
    // Tentar enviar formulário vazio
    await page.click('button[type="submit"]');
    
    // Verificar validação HTML5
    const nameInput = page.locator('input[name="name"]');
    const emailInput = page.locator('input[name="email"]');
    
    expect(await nameInput.evaluate(el => (el as HTMLInputElement).validity.valid)).toBe(false);
    expect(await emailInput.evaluate(el => (el as HTMLInputElement).validity.valid)).toBe(false);
  });

  test('deve validar formato de email', async ({ page }) => {
    await page.click('a[href="#contact"]');
    
    // Preencher com email inválido
    await page.fill('input[name="name"]', 'João Silva');
    await page.fill('input[name="email"]', 'email-invalido');
    
    await page.click('button[type="submit"]');
    
    // Verificar validação de email
    const emailInput = page.locator('input[name="email"]');
    expect(await emailInput.evaluate(el => (el as HTMLInputElement).validity.valid)).toBe(false);
  });

  test('deve mostrar loading durante envio do formulário', async ({ page }) => {
    await page.click('a[href="#contact"]');
    
    // Preencher formulário
    await page.fill('input[name="name"]', 'João Silva');
    await page.fill('input[name="email"]', '<EMAIL>');
    
    // Interceptar e atrasar resposta
    await page.route('**/api/leads', async route => {
      await new Promise(resolve => setTimeout(resolve, 2000));
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ success: true, message: 'Lead criado com sucesso' })
      });
    });
    
    // Enviar formulário
    await page.click('button[type="submit"]');
    
    // Verificar estado de loading
    await expect(page.locator('button[type="submit"]:has-text("Enviando")')).toBeVisible();
    await expect(page.locator('button[type="submit"][disabled]')).toBeVisible();
    
    // Aguardar conclusão
    await expect(page.locator('button[type="submit"]:has-text("Enviando")')).not.toBeVisible({ timeout: 5000 });
  });

  test('deve lidar com erro de envio do formulário', async ({ page }) => {
    await page.click('a[href="#contact"]');
    
    // Preencher formulário
    await page.fill('input[name="name"]', 'João Silva');
    await page.fill('input[name="email"]', '<EMAIL>');
    
    // Simular erro do servidor
    await page.route('**/api/leads', route => {
      route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ success: false, message: 'Erro interno do servidor' })
      });
    });
    
    // Enviar formulário
    await page.click('button[type="submit"]');
    
    // Verificar mensagem de erro
    await expect(page.locator('.bg-red-50')).toBeVisible({ timeout: 10000 });
    await expect(page.locator('text=Erro ao enviar')).toBeVisible();
  });

  test('deve funcionar toggle de preços (mensal/anual)', async ({ page }) => {
    await page.click('a[href="#pricing"]');
    await expect(page.locator('#pricing')).toBeInViewport();
    
    // Verificar estado inicial (mensal)
    const toggle = page.locator('#billing-toggle');
    await expect(toggle).toBeVisible();
    
    // Verificar preços mensais estão visíveis
    await expect(page.locator('[data-billing="monthly"]')).toBeVisible();
    
    // Clicar no toggle para anual
    await toggle.click();
    
    // Verificar preços anuais estão visíveis
    await expect(page.locator('[data-billing="yearly"]')).toBeVisible();
    
    // Voltar para mensal
    await toggle.click();
    await expect(page.locator('[data-billing="monthly"]')).toBeVisible();
  });

  test('deve rastrear cliques em planos', async ({ page }) => {
    await page.click('a[href="#pricing"]');
    
    // Interceptar chamadas de analytics
    const analyticsRequests: any[] = [];
    page.on('request', request => {
      if (request.url().includes('/api/analytics/landing-page')) {
        analyticsRequests.push(request.postDataJSON());
      }
    });
    
    // Clicar em um plano
    await page.click('[data-plan="basic"] .cta-button');
    
    // Aguardar um pouco para analytics serem enviados
    await page.waitForTimeout(1000);
    
    // Verificar se analytics foram enviados
    const planClickEvents = analyticsRequests.filter(req => req.event === 'plan_click');
    expect(planClickEvents.length).toBeGreaterThan(0);
    expect(planClickEvents[0].plan).toBe('basic');
  });

  test('deve rastrear scroll depth', async ({ page }) => {
    const analyticsRequests: any[] = [];
    page.on('request', request => {
      if (request.url().includes('/api/analytics/landing-page')) {
        analyticsRequests.push(request.postDataJSON());
      }
    });
    
    // Fazer scroll até o final da página
    await page.evaluate(() => {
      window.scrollTo(0, document.body.scrollHeight);
    });
    
    // Aguardar analytics serem enviados
    await page.waitForTimeout(2000);
    
    // Verificar se eventos de scroll foram enviados
    const scrollEvents = analyticsRequests.filter(req => req.event === 'scroll_depth');
    expect(scrollEvents.length).toBeGreaterThan(0);
    
    // Deve ter pelo menos um evento de 100% de scroll
    const fullScrollEvent = scrollEvents.find(event => event.percentage === 100);
    expect(fullScrollEvent).toBeTruthy();
  });

  test('deve funcionar em dispositivos móveis', async ({ page }) => {
    // Simular dispositivo móvel
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Verificar se formulário ainda funciona
    await page.click('a[href="#contact"]');
    
    await page.fill('input[name="name"]', 'João Mobile');
    await page.fill('input[name="email"]', '<EMAIL>');
    
    // Mock da resposta
    await page.route('**/api/leads', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ success: true })
      });
    });
    
    await page.click('button[type="submit"]');
    
    // Verificar sucesso
    await expect(page.locator('.bg-green-50')).toBeVisible({ timeout: 10000 });
  });

  test('deve limpar formulário após envio bem-sucedido', async ({ page }) => {
    await page.click('a[href="#contact"]');
    
    // Preencher formulário
    await page.fill('input[name="name"]', 'João Silva');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('textarea[name="message"]', 'Mensagem de teste');
    
    // Mock da resposta
    await page.route('**/api/leads', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ success: true })
      });
    });
    
    await page.click('button[type="submit"]');
    
    // Aguardar sucesso
    await expect(page.locator('.bg-green-50')).toBeVisible({ timeout: 10000 });
    
    // Verificar se campos foram limpos
    await expect(page.locator('input[name="name"]')).toHaveValue('');
    await expect(page.locator('input[name="email"]')).toHaveValue('');
    await expect(page.locator('textarea[name="message"]')).toHaveValue('');
  });
});
