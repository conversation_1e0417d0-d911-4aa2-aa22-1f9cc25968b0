@echo off
echo ========================================
echo      TESTANDO APENAS O BACKEND
echo ========================================
echo.

echo [1/3] Verificando Jest...
cd backend
if not exist "node_modules\jest" (
    echo ❌ Jest nao encontrado!
    echo Execute: scripts\install-backend-tests.bat
    pause
    exit /b 1
)

echo ✅ Jest encontrado!

echo.
echo [2/3] Executando testes do backend...
npx jest --silent --passWithNoTests

set BACKEND_RESULT=%errorlevel%

echo.
echo [3/3] Resultado:

if %BACKEND_RESULT% equ 0 (
    echo ========================================
    echo      ✅ TESTES DO BACKEND PASSARAM!
    echo ========================================
    echo.
    echo O backend esta pronto para deploy!
    echo.
    echo Proximo passo:
    echo - scripts\fix-cors-deploy.bat
) else (
    echo ========================================
    echo      ❌ TESTES DO BACKEND FALHARAM!
    echo ========================================
    echo.
    echo Corrija os problemas antes de fazer deploy.
    echo.
    echo Para debug:
    echo - npx jest --verbose
    echo - npx jest tests/simple.test.js
)

echo.
cd ..
pause
exit /b %BACKEND_RESULT%
