@echo off
echo ========================================
echo   TESTANDO APENAS SOCIAL MEDIA PAGE
echo ========================================
echo.

cd frontend

echo [1/3] Testando SocialMediaPage...
npx tsc --noEmit --skipLibCheck src/pages/SocialMediaPage.tsx

set PAGE_RESULT=%errorlevel%

echo.
echo [2/3] Testando componentes Social Media...
npx tsc --noEmit --skipLibCheck src/components/social-media/PostHistory.tsx src/components/social-media/MediaUpload.tsx src/components/social-media/PostScheduler.tsx

set COMPONENTS_RESULT=%errorlevel%

echo.
echo [3/3] Testando mediaService...
npx tsc --noEmit --skipLibCheck src/services/mediaService.ts

set SERVICE_RESULT=%errorlevel%

echo.
echo ========================================
echo              RESULTADO
echo ========================================

if %PAGE_RESULT% equ 0 (
    echo ✅ SocialMediaPage.tsx - OK
) else (
    echo ❌ SocialMediaPage.tsx - ERRO
)

if %COMPONENTS_RESULT% equ 0 (
    echo ✅ Componentes Social Media - OK
) else (
    echo ❌ Componentes Social Media - ERRO
)

if %SERVICE_RESULT% equ 0 (
    echo ✅ mediaService.ts - OK
) else (
    echo ❌ mediaService.ts - ERRO
)

if %PAGE_RESULT% equ 0 if %COMPONENTS_RESULT% equ 0 if %SERVICE_RESULT% equ 0 (
    echo.
    echo ========================================
    echo      🎉 SOCIAL MEDIA FASE 2 PRONTA!
    echo ========================================
    echo.
    echo Todos os problemas foram corrigidos!
    echo.
    echo A Fase 2 esta implementada:
    echo - ✅ Historico de Publicacoes
    echo - ✅ Upload Real de Media  
    echo - ✅ Agendamento Basico
    echo - ✅ Interface com Abas
    echo - ✅ Tipos TypeScript
    echo - ✅ Validacoes
    echo.
    echo Para usar:
    echo 1. npm run dev
    echo 2. Acesse /social-media
    echo 3. Teste as funcionalidades
) else (
    echo.
    echo ❌ Ainda ha problemas para corrigir
)

echo.
cd ..
pause
