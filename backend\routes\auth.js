import express from 'express';
import { body, validationResult } from 'express-validator';
import User from '../models/User.js';
import JWTUtils from '../utils/jwt.js';
import AuthLogger from '../utils/authLogger.js';
import { loginLimiter, passwordResetLimiter } from '../middleware/rateLimiter.js';
import { authenticateToken, optionalAuth } from '../middleware/auth.js';
import config from '../config.js';
import fs from 'fs-extra';
import path from 'path';
import { fileURLToPath } from 'url';

// ES module equivalent of __dirname - compatível com testes
let __dirname;
try {
  const __filename = fileURLToPath(import.meta.url);
  __dirname = path.dirname(__filename);
} catch (error) {
  // Fallback para testes Jest
  __dirname = path.join(process.cwd(), 'routes');
}

const router = express.Router();

// Função para carregar dados do plano
async function loadPlanData(planId) {
  try {
    if (!planId) return null;
    
    const plansPath = path.join(__dirname, '../data/plans.json');
    const plansData = await fs.readFile(plansPath, 'utf8');
    const plans = JSON.parse(plansData);
    
    return plans.find(plan => plan.id === planId) || null;
  } catch (error) {
    console.error('Erro ao carregar dados do plano:', error);
    return null;
  }
}

// Validações
const loginValidation = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Email inválido'),
  body('password')
    .isLength({ min: 1 })
    .withMessage('Senha é obrigatória')
];

const registerValidation = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Email inválido'),
  body('password')
    .isLength({ min: config.security.passwordMinLength })
    .withMessage(`Senha deve ter pelo menos ${config.security.passwordMinLength} caracteres`)
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('Senha deve conter pelo menos uma letra minúscula, uma maiúscula e um número'),
  body('name')
    .isLength({ min: 2, max: 100 })
    .withMessage('Nome deve ter entre 2 e 100 caracteres')
    .trim()
];

const changePasswordValidation = [
  body('currentPassword')
    .isLength({ min: 1 })
    .withMessage('Senha atual é obrigatória'),
  body('newPassword')
    .isLength({ min: config.security.passwordMinLength })
    .withMessage(`Nova senha deve ter pelo menos ${config.security.passwordMinLength} caracteres`)
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('Nova senha deve conter pelo menos uma letra minúscula, uma maiúscula e um número')
];

// Função para extrair IP e User-Agent
const getClientInfo = (req) => ({
  ip: req.ip || 'unknown',
  userAgent: req.get('User-Agent') || 'unknown'
});

// POST /api/auth/login
router.post('/login', loginLimiter, loginValidation, async (req, res) => {
  try {
    // Verificar erros de validação
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Dados inválidos',
        errors: errors.array()
      });
    }

    const { email, password, rememberMe = false } = req.body;
    const { ip, userAgent } = getClientInfo(req);

    // Buscar usuário
    const user = await User.findByEmail(email);
    
    if (!user) {
      await AuthLogger.logLogin(null, email, false, ip, userAgent, { reason: 'user_not_found' });
      return res.status(401).json({
        success: false,
        message: 'Email ou senha incorretos'
      });
    }

    // Verificar se a conta está bloqueada
    if (user.isLocked()) {
      await AuthLogger.logLogin(user.id, email, false, ip, userAgent, { reason: 'account_locked' });
      return res.status(423).json({
        success: false,
        message: 'Conta temporariamente bloqueada devido a múltiplas tentativas de login'
      });
    }

    // Verificar se a conta está ativa
    if (!user.isActive) {
      await AuthLogger.logLogin(user.id, email, false, ip, userAgent, { reason: 'account_inactive' });
      return res.status(401).json({
        success: false,
        message: 'Conta desativada'
      });
    }

    // Verificar senha
    const isValidPassword = await user.validatePassword(password);
    
    if (!isValidPassword) {
      await user.incrementLoginAttempts();
      await AuthLogger.logLogin(user.id, email, false, ip, userAgent, { reason: 'invalid_password' });
      
      return res.status(401).json({
        success: false,
        message: 'Email ou senha incorretos'
      });
    }

    // Login bem-sucedido
    await user.resetLoginAttempts();
    await AuthLogger.logLogin(user.id, email, true, ip, userAgent);

    // Gerar tokens
    const tokens = JWTUtils.generateTokens(user);

    // Configurar expiração baseada no "lembrar-me"
    if (rememberMe) {
      // Se "lembrar-me" estiver marcado, estender a validade
      tokens.accessToken = JWTUtils.generateAccessToken(user);
      tokens.refreshToken = JWTUtils.generateRefreshToken(user);
    }

    // Carregar dados do plano do usuário
    const planData = await loadPlanData(user.planId);
    const userData = user.toSafeJSON();
    
    // Adicionar dados do plano ao usuário
    if (planData) {
      userData.plan = planData;
    }

    res.json({
      success: true,
      message: 'Login realizado com sucesso',
      data: {
        user: userData,
        ...tokens
      }
    });

  } catch (error) {
    console.error('Erro no login:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor'
    });
  }
});

// POST /api/auth/register
router.post('/register', registerValidation, async (req, res) => {
  try {
    // Verificar erros de validação
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Dados inválidos',
        errors: errors.array()
      });
    }

    const { email, password, name, role = 'USER' } = req.body;
    const { ip, userAgent } = getClientInfo(req);

    // Verificar se o email já existe
    const existingUser = await User.findByEmail(email);
    if (existingUser) {
      return res.status(409).json({
        success: false,
        message: 'Email já está em uso'
      });
    }

    // Criar usuário
    const userData = {
      email,
      password,
      name,
      role: role === 'ADMIN' ? 'USER' : role, // Não permitir criação direta de admin via API
      isActive: true,
      emailVerified: false
    };

    const newUser = await User.create(userData);
    await AuthLogger.logUserCreated(newUser.id, email, 'self_registration', ip, userAgent);

    res.status(201).json({
      success: true,
      message: 'Usuário criado com sucesso',
      data: {
        user: newUser.toSafeJSON()
      }
    });

  } catch (error) {
    console.error('Erro no registro:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Erro interno do servidor'
    });
  }
});

// POST /api/auth/refresh
router.post('/refresh', async (req, res) => {
  try {
    const { refreshToken } = req.body;

    if (!refreshToken) {
      return res.status(401).json({
        success: false,
        message: 'Refresh token requerido'
      });
    }

    // Verificar refresh token
    const decoded = JWTUtils.verifyToken(refreshToken);
    
    if (decoded.type !== 'refresh') {
      return res.status(401).json({
        success: false,
        message: 'Token inválido'
      });
    }

    // Buscar usuário
    const user = await User.findById(decoded.userId);
    
    if (!user || !user.isActive) {
      return res.status(401).json({
        success: false,
        message: 'Usuário não encontrado ou inativo'
      });
    }

    // Gerar novos tokens
    const tokens = JWTUtils.generateTokens(user);

    res.json({
      success: true,
      message: 'Tokens renovados com sucesso',
      data: {
        user: user.toSafeJSON(),
        ...tokens
      }
    });

  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        message: 'Refresh token expirado',
        code: 'REFRESH_TOKEN_EXPIRED'
      });
    }
    
    console.error('Erro no refresh:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor'
    });
  }
});

// POST /api/auth/logout
router.post('/logout', authenticateToken, async (req, res) => {
  try {
    const { ip, userAgent } = getClientInfo(req);
    
    await AuthLogger.logLogout(req.user.id, req.user.email, ip, userAgent);

    res.json({
      success: true,
      message: 'Logout realizado com sucesso'
    });

  } catch (error) {
    console.error('Erro no logout:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor'
    });
  }
});

// GET /api/auth/me
router.get('/me', authenticateToken, async (req, res) => {
  try {
    // Buscar dados atualizados do usuário
    const user = await User.findById(req.user.id);
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'Usuário não encontrado'
      });
    }

    res.json({
      success: true,
      data: {
        user: user.toSafeJSON()
      }
    });

  } catch (error) {
    console.error('Erro ao buscar perfil:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor'
    });
  }
});

// POST /api/auth/change-password
router.post('/change-password', authenticateToken, changePasswordValidation, async (req, res) => {
  try {
    // Verificar erros de validação
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Dados inválidos',
        errors: errors.array()
      });
    }

    const { currentPassword, newPassword } = req.body;
    const { ip, userAgent } = getClientInfo(req);

    // Verificar senha atual
    const isValidPassword = await req.user.validatePassword(currentPassword);
    
    if (!isValidPassword) {
      return res.status(401).json({
        success: false,
        message: 'Senha atual incorreta'
      });
    }

    // Atualizar senha
    await req.user.updatePassword(newPassword);
    await AuthLogger.logPasswordChange(req.user.id, req.user.email, ip, userAgent);

    res.json({
      success: true,
      message: 'Senha alterada com sucesso'
    });

  } catch (error) {
    console.error('Erro ao alterar senha:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor'
    });
  }
});

// POST /api/auth/forgot-password
router.post('/forgot-password', passwordResetLimiter, async (req, res) => {
  try {
    const { email } = req.body;
    const { ip, userAgent } = getClientInfo(req);

    if (!email) {
      return res.status(400).json({
        success: false,
        message: 'Email é obrigatório'
      });
    }

    const user = await User.findByEmail(email);
    
    // Sempre retornar sucesso para não revelar se o email existe
    if (user) {
      const resetToken = JWTUtils.generatePasswordResetToken(user);
      await AuthLogger.logPasswordReset(email, ip, userAgent, true);
      
      // Aqui você implementaria o envio do email
      console.log(`Token de reset para ${email}: ${resetToken}`);
    } else {
      await AuthLogger.logPasswordReset(email, ip, userAgent, false);
    }

    res.json({
      success: true,
      message: 'Se o email existir, você receberá instruções para redefinir sua senha'
    });

  } catch (error) {
    console.error('Erro no forgot password:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor'
    });
  }
});

// PUT /api/auth/profile
router.put('/profile', authenticateToken, async (req, res) => {
  try {
    const { name, profile } = req.body;
    const { ip, userAgent } = getClientInfo(req);

    // Validar dados
    if (name && (typeof name !== 'string' || name.trim().length < 2)) {
      return res.status(400).json({
        success: false,
        message: 'Nome deve ter pelo menos 2 caracteres'
      });
    }

    // Atualizar dados do usuário
    const updateData = {};
    
    if (name) {
      updateData.name = name.trim();
    }

    if (profile && typeof profile === 'object') {
      updateData.profile = {
        ...req.user.profile,
        ...profile
      };
    }

    // Aplicar atualizações
    Object.assign(req.user, updateData);
    await req.user.save();

    await AuthLogger.logProfileUpdate(req.user.id, req.user.email, ip, userAgent);

    res.json({
      success: true,
      message: 'Perfil atualizado com sucesso',
      data: {
        user: req.user.toSafeJSON()
      }
    });

  } catch (error) {
    console.error('Erro ao atualizar perfil:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor'
    });
  }
});

// GET /api/auth/verify-token
router.get('/verify-token', optionalAuth, (req, res) => {
  if (req.user) {
    res.json({
      success: true,
      valid: true,
      data: {
        user: req.user.toSafeJSON()
      }
    });
  } else {
    res.json({
      success: true,
      valid: false
    });
  }
});


// POST /api/auth/register-after-payment
router.post('/register-after-payment', async (req, res) => {
  try {
    const {
      name,
      email,
      password,
      phone,
      organization,
      position,
      sessionId,
      planType,
      billingCycle
    } = req.body;

    // Validar dados obrigatórios
    if (!name || !email || !password || !sessionId) {
      return res.status(400).json({
        success: false,
        message: 'Dados obrigatórios não fornecidos'
      });
    }

    const { ip, userAgent } = getClientInfo(req);

    // Verificar se a sessão de pagamento é válida (se Stripe estiver configurado)
    if (process.env.STRIPE_SECRET_KEY) {
      try {
        const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
        const session = await stripe.checkout.sessions.retrieve(sessionId);

        if (!session || session.payment_status !== 'paid') {
          return res.status(400).json({
            success: false,
            message: 'Sessão de pagamento inválida ou não paga'
          });
        }
      } catch (stripeError) {
        console.error('Erro ao verificar sessão do Stripe:', stripeError);
        return res.status(400).json({
          success: false,
          message: 'Erro ao verificar pagamento'
        });
      }
    }

    // Verificar se o email já existe
    const existingUser = await User.findByEmail(email);
    if (existingUser) {
      return res.status(409).json({
        success: false,
        message: 'Email já está em uso'
      });
    }

    // Criar novo usuário com dados da assinatura
    const userData = {
      name,
      email,
      password,
      phone: phone || '',
      organization: organization || '',
      position: position || '',
      role: 'USER',
      isActive: true,
      subscription: {
        planType,
        billingCycle,
        stripeSessionId: sessionId,
        status: 'active',
        startDate: new Date().toISOString(),
        nextBillingDate: calculateNextBillingDate(billingCycle)
      }
    };

    const user = await User.create(userData);
    await AuthLogger.logLogin(user.id, email, true, ip, userAgent, {
      reason: 'registration_after_payment',
      planType,
      billingCycle
    });

    // Carregar dados do plano
    const planData = await loadPlanData(user.planId);
    const userResponse = user.toSafeJSON();

    if (planData) {
      userResponse.plan = planData;
    }

    res.status(201).json({
      success: true,
      message: 'Usuário criado com sucesso',
      data: {
        user: userResponse
      }
    });

  } catch (error) {
    console.error('Erro ao registrar usuário após pagamento:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor'
    });
  }
});

// Função auxiliar para calcular próxima data de cobrança
function calculateNextBillingDate(billingCycle) {
  const now = new Date();

  switch (billingCycle) {
    case 'month':
      now.setMonth(now.getMonth() + 1);
      break;
    case 'year':
      now.setFullYear(now.getFullYear() + 1);
      break;
    default:
      // Para pagamentos únicos, não há próxima cobrança
      return null;
  }

  return now.toISOString();
}

export default router;