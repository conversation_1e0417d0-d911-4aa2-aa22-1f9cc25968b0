// Teste simples para verificar se o Jest está funcionando

describe('Teste Básico', () => {
  test('deve somar dois números corretamente', () => {
    expect(2 + 2).toBe(4);
  });

  test('deve verificar se uma string contém texto', () => {
    expect('Pro-Mandato').toContain('Mandato');
  });

  test('deve verificar se um array contém um elemento', () => {
    const frutas = ['maçã', 'banana', 'laranja'];
    expect(frutas).toContain('banana');
  });

  test('deve verificar se um objeto tem propriedades', () => {
    const usuario = {
      nome: 'João',
      email: '<EMAIL>',
      ativo: true
    };

    expect(usuario).toHaveProperty('nome');
    expect(usuario).toHaveProperty('email');
    expect(usuario.ativo).toBe(true);
  });

  test('deve trabalhar com promises', async () => {
    const promiseResolvida = Promise.resolve('sucesso');
    await expect(promiseResolvida).resolves.toBe('sucesso');
  });

  test('deve trabalhar com funções mock', () => {
    const mockFn = jest.fn();
    mockFn('arg1', 'arg2');
    
    expect(mockFn).toHaveBeenCalled();
    expect(mockFn).toHaveBeenCalledWith('arg1', 'arg2');
    expect(mockFn).toHaveBeenCalledTimes(1);
  });
});
