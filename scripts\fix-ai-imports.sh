#!/bin/bash

echo "========================================"
echo "   CORRIGINDO IMPORTS DA AI PAGE"
echo "========================================"
echo

cd frontend

echo "[1/4] Limpando cache do TypeScript..."
rm -rf node_modules/.cache
rm -rf .vite
rm -f tsconfig.tsbuildinfo

echo "[2/4] Reinstalando dependências..."
npm install

echo "[3/4] Verificando se os componentes existem..."
echo "✓ PredictiveAnalytics:" $(ls -la src/components/ai/PredictiveAnalytics.tsx 2>/dev/null && echo "EXISTS" || echo "MISSING")
echo "✓ SmartResponses:" $(ls -la src/components/ai/SmartResponses.tsx 2>/dev/null && echo "EXISTS" || echo "MISSING")
echo "✓ SentimentAnalysis:" $(ls -la src/components/ai/SentimentAnalysis.tsx 2>/dev/null && echo "EXISTS" || echo "MISSING")
echo "✓ AutoCategorization:" $(ls -la src/components/ai/AutoCategorization.tsx 2>/dev/null && echo "EXISTS" || echo "MISSING")

echo "[4/4] Testando build..."
npm run build > build_test.log 2>&1

if [ $? -eq 0 ]; then
    echo "✅ Build OK - Todos os imports funcionando!"
    echo
    echo "Iniciando servidor de desenvolvimento..."
    npm run dev
else
    echo "❌ Build FALHOU - Verificando erros:"
    cat build_test.log
fi

echo
cd ..
read -p "Pressione Enter para continuar..."
