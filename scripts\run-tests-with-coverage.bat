@echo off
echo ========================================
echo   EXECUTANDO TESTES COM COBERTURA
echo ========================================
echo.

echo [1/3] Limpando relatorios anteriores...
if exist "backend\coverage" rmdir /s /q "backend\coverage"
if exist "frontend\coverage" rmdir /s /q "frontend\coverage"
if exist "test-results" rmdir /s /q "test-results"
if exist "playwright-report" rmdir /s /q "playwright-report"

echo.
echo [2/3] Executando testes com cobertura...

echo.
echo === BACKEND (com cobertura) ===
cd backend
npm run test:coverage
set BACKEND_EXIT_CODE=%errorlevel%
cd ..

echo.
echo === FRONTEND (com cobertura) ===
cd frontend
npm run test:coverage
set FRONTEND_EXIT_CODE=%errorlevel%
cd ..

echo.
echo === E2E (com relatorios) ===
npm run test:e2e
set E2E_EXIT_CODE=%errorlevel%

echo.
echo [3/3] Gerando relatorio consolidado...

echo.
echo ========================================
echo         RELATORIO DE COBERTURA
echo ========================================
echo.

if exist "backend\coverage\lcov-report\index.html" (
    echo ✅ Backend: backend\coverage\lcov-report\index.html
) else (
    echo ❌ Backend: Relatorio nao gerado
)

if exist "frontend\coverage\index.html" (
    echo ✅ Frontend: frontend\coverage\index.html
) else (
    echo ❌ Frontend: Relatorio nao gerado
)

if exist "playwright-report\index.html" (
    echo ✅ E2E: playwright-report\index.html
) else (
    echo ❌ E2E: Relatorio nao gerado
)

echo.
echo ========================================
echo           RESUMO DOS TESTES
echo ========================================
echo.

set /a TOTAL_FAILURES=%BACKEND_EXIT_CODE%+%FRONTEND_EXIT_CODE%+%E2E_EXIT_CODE%

if %BACKEND_EXIT_CODE% equ 0 (
    echo ✅ Backend: PASSOU
) else (
    echo ❌ Backend: FALHOU
)

if %FRONTEND_EXIT_CODE% equ 0 (
    echo ✅ Frontend: PASSOU
) else (
    echo ❌ Frontend: FALHOU
)

if %E2E_EXIT_CODE% equ 0 (
    echo ✅ E2E: PASSOU
) else (
    echo ❌ E2E: FALHOU
)

echo.
if %TOTAL_FAILURES% equ 0 (
    echo 🎉 TODOS OS TESTES PASSARAM!
    echo.
    echo Abrir relatorios de cobertura?
    choice /c YN /m "Pressione Y para abrir ou N para sair"
    if !errorlevel! equ 1 (
        if exist "backend\coverage\lcov-report\index.html" start "" "backend\coverage\lcov-report\index.html"
        if exist "frontend\coverage\index.html" start "" "frontend\coverage\index.html"
        if exist "playwright-report\index.html" start "" "playwright-report\index.html"
    )
) else (
    echo ❌ ALGUNS TESTES FALHARAM!
    echo.
    echo Abrir relatorios para investigacao?
    choice /c YN /m "Pressione Y para abrir ou N para sair"
    if !errorlevel! equ 1 (
        if exist "playwright-report\index.html" start "" "playwright-report\index.html"
    )
)

echo.
pause
exit /b %TOTAL_FAILURES%
