@echo off
echo ========================================
echo   TESTANDO SOCIAL MEDIA COM DEBUG
echo ========================================
echo.

cd frontend

echo [1/3] Verificando se o projeto compila...
npm run build > build_log.txt 2>&1

if %errorlevel% equ 0 (
    echo ✅ Build OK - Projeto compila sem erros
    
    echo.
    echo [2/3] Iniciando servidor de desenvolvimento...
    echo.
    echo ========================================
    echo      🚀 SERVIDOR INICIANDO...
    echo ========================================
    echo.
    echo O servidor sera iniciado em modo debug.
    echo.
    echo Para testar a Social Media:
    echo 1. Acesse: http://localhost:5173/social-media
    echo 2. Clique em "Criar Contas Mock para Teste"
    echo 3. Teste os checkboxes em "Publicar em"
    echo 4. Teste os radio buttons em "Quando publicar"
    echo 5. Verifique o console do navegador para logs
    echo.
    echo Pressione Ctrl+C para parar o servidor
    echo.
    
    npm run dev
    
) else (
    echo ❌ Build FALHOU - Verificando erros:
    echo.
    type build_log.txt
    echo.
    echo Corrija os erros acima antes de testar.
)

echo.
cd ..
pause
