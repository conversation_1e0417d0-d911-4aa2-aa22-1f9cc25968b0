/**
 * Serviço centralizado para comunicação com a API do backend
 */

import ENV_CONFIG from '../config/environment';

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  timestamp?: string;
}

export interface RequestOptions extends RequestInit {
  timeout?: number;
  retries?: number;
}

export class ApiError extends Error {
  constructor(
    message: string,
    public status?: number,
    public code?: string,
    public details?: any
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

class ApiService {
  private baseURL: string;
  private defaultTimeout: number;
  private defaultRetries: number;

  constructor() {
    this.baseURL = ENV_CONFIG.API_BASE_URL;
    this.defaultTimeout = 30000;
    this.defaultRetries = 3;
  }

  /**
   * Fazer requisição HTTP com retry automático e melhor tratamento de erros
   */
  private async makeRequest<T>(
    endpoint: string, 
    options: RequestOptions = {}
  ): Promise<ApiResponse<T>> {
    const { timeout = this.defaultTimeout, retries = this.defaultRetries, ...fetchOptions } = options;

    const url = `${this.baseURL}${endpoint}`;
    const token = localStorage.getItem('accessToken');

    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Bearer ${token}` }),
        ...fetchOptions.headers,
      },
      ...fetchOptions,
    };

    let lastError: Error | null = null;

    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), timeout);

        const response = await fetch(url, {
          ...config,
          signal: controller.signal,
        });

        clearTimeout(timeoutId);

        // Tentar parsear a resposta JSON
        let responseData: any;
        try {
          responseData = await response.json();
        } catch (parseError) {
          responseData = {
            success: false,
            error: 'Resposta inválida do servidor',
            message: response.statusText
          };
        }

        if (!response.ok) {
          throw new ApiError(
            responseData.error || responseData.message || `HTTP ${response.status}: ${response.statusText}`,
            response.status,
            responseData.code,
            responseData
          );
        }

        return responseData;

      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));

        // Log do erro para debugging
        console.error(`API Request failed (attempt ${attempt + 1}/${retries + 1}):`, {
          url,
          method: config.method || 'GET',
          error: lastError.message,
          attempt: attempt + 1
        });

        // Se não é o último attempt, aguardar antes de tentar novamente
        if (attempt < retries) {
          const delay = Math.min(1000 * Math.pow(2, attempt), 5000); // Exponential backoff
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    // Se chegou aqui, todas as tentativas falharam
    if (!lastError) {
      lastError = new Error('Erro desconhecido na comunicação com o servidor');
    }

    throw new ApiError(
      `Falha na comunicação com o servidor após ${retries + 1} tentativas: ${lastError.message}`,
      0,
      'NETWORK_ERROR',
      { originalError: lastError.message, attempts: retries + 1 }
    );
  }

  /**
   * GET request
   */
  async get<T>(endpoint: string, options?: RequestOptions): Promise<ApiResponse<T>> {
    return this.makeRequest<T>(endpoint, { ...options, method: 'GET' });
  }

  /**
   * POST request
   */
  async post<T>(endpoint: string, data?: any, options?: RequestOptions): Promise<ApiResponse<T>> {
    return this.makeRequest<T>(endpoint, {
      ...options,
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  /**
   * PUT request
   */
  async put<T>(endpoint: string, data?: any, options?: RequestOptions): Promise<ApiResponse<T>> {
    return this.makeRequest<T>(endpoint, {
      ...options,
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  /**
   * DELETE request
   */
  async delete<T>(endpoint: string, options?: RequestOptions): Promise<ApiResponse<T>> {
    return this.makeRequest<T>(endpoint, { ...options, method: 'DELETE' });
  }

  /**
   * Upload de arquivo
   */
  async upload<T>(endpoint: string, file: File, options?: Omit<RequestOptions, 'headers'>): Promise<ApiResponse<T>> {
    const formData = new FormData();
    formData.append('file', file);
    
    const token = localStorage.getItem('accessToken');

    return this.makeRequest<T>(endpoint, {
      ...options,
      method: 'POST',
      body: formData,
      headers: {
        ...(token && { 'Authorization': `Bearer ${token}` }),
      },
    });
  }

  /**
   * Verificar se o servidor está online
   */
  async healthCheck(): Promise<boolean> {
    try {
      const response = await this.get('/health', { timeout: 5000, retries: 1 });
      return response.success;
    } catch (error) {
      console.warn('Health check failed:', error);
      return false;
    }
  }
}

export const apiService = new ApiService();
export default apiService;