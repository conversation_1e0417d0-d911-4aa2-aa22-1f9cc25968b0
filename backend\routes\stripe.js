import express from 'express';
import Stripe from 'stripe';

// Verificar se a chave secreta do Stripe está configurada
if (!process.env.STRIPE_SECRET_KEY) {
  console.error('❌ STRIPE_SECRET_KEY não está configurada nas variáveis de ambiente');
  throw new Error('STRIPE_SECRET_KEY é obrigatória');
}

console.log('✅ Stripe Secret Key encontrada:', process.env.STRIPE_SECRET_KEY ? 'Configurada' : 'Não configurada');

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);
const router = express.Router();

// Endpoint de debug para verificar configurações
router.get('/debug', (req, res) => {
  res.json({
    success: true,
    data: {
      stripeConfigured: !!process.env.STRIPE_SECRET_KEY,
      stripeKeyPrefix: process.env.STRIPE_SECRET_KEY ? process.env.STRIPE_SECRET_KEY.substring(0, 7) + '...' : 'Not configured',
      frontendUrl: process.env.FRONTEND_URL,
      nodeEnv: process.env.NODE_ENV,
      timestamp: new Date().toISOString()
    }
  });
});

// Middleware para log de debug
router.use((req, res, next) => {
  console.log(`[STRIPE] ${req.method} ${req.path}`, {
    origin: req.headers.origin,
    userAgent: req.headers['user-agent'],
    body: req.method === 'POST' ? req.body : undefined
  });
  next();
});

// Criar sessão de checkout
router.post('/create-checkout-session', async (req, res) => {
  try {
    console.log('📥 Recebendo requisição para criar checkout session:', req.body);

    const {
      priceId,
      planName,
      billingCycle,
      successUrl,
      cancelUrl,
      customerEmail
    } = req.body;

    // Validações mais detalhadas
    if (!priceId) {
      console.error('❌ Price ID não fornecido');
      return res.status(400).json({
        success: false,
        error: 'Price ID é obrigatório'
      });
    }

    console.log('✅ Price ID válido:', priceId);

    // Verificar se as URLs estão configuradas
    const defaultSuccessUrl = `${process.env.FRONTEND_URL}/payment-success?session_id={CHECKOUT_SESSION_ID}`;
    const defaultCancelUrl = `${process.env.FRONTEND_URL}/pricing`;

    console.log('🔗 URLs configuradas:', {
      successUrl: successUrl || defaultSuccessUrl,
      cancelUrl: cancelUrl || defaultCancelUrl,
      frontendUrl: process.env.FRONTEND_URL
    });

    const sessionConfig = {
      mode: 'subscription',
      payment_method_types: ['card'],
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      success_url: successUrl || defaultSuccessUrl,
      cancel_url: cancelUrl || defaultCancelUrl,
      allow_promotion_codes: true,
      billing_address_collection: 'required',
      customer_creation: 'always',
      locale: 'pt-BR',
      metadata: {
        planName: planName || 'Unknown',
        billingCycle: billingCycle || 'month'
      }
    };

    // Adicionar email do cliente se fornecido
    if (customerEmail) {
      sessionConfig.customer_email = customerEmail;
      console.log('📧 Email do cliente adicionado:', customerEmail);
    }

    console.log('⚙️ Configuração da sessão:', JSON.stringify(sessionConfig, null, 2));

    // Tentar criar a sessão no Stripe
    console.log('🔄 Criando sessão no Stripe...');
    const session = await stripe.checkout.sessions.create(sessionConfig);

    console.log('✅ Sessão criada com sucesso:', {
      sessionId: session.id,
      url: session.url,
      status: session.status
    });

    res.json({
      success: true,
      data: {
        sessionId: session.id,
        url: session.url
      }
    });

  } catch (error) {
    console.error('❌ Erro detalhado ao criar sessão de checkout:', {
      message: error.message,
      type: error.type,
      code: error.code,
      statusCode: error.statusCode,
      stack: error.stack
    });

    // Retornar erro mais específico baseado no tipo de erro do Stripe
    let errorMessage = 'Erro interno do servidor';
    let statusCode = 500;

    if (error.type === 'StripeCardError') {
      errorMessage = 'Erro no cartão de crédito';
      statusCode = 400;
    } else if (error.type === 'StripeRateLimitError') {
      errorMessage = 'Muitas requisições. Tente novamente em alguns segundos.';
      statusCode = 429;
    } else if (error.type === 'StripeInvalidRequestError') {
      errorMessage = `Erro na requisição: ${error.message}`;
      statusCode = 400;
    } else if (error.type === 'StripeAPIError') {
      errorMessage = 'Erro na API do Stripe. Tente novamente.';
      statusCode = 502;
    } else if (error.type === 'StripeConnectionError') {
      errorMessage = 'Erro de conexão com o Stripe. Tente novamente.';
      statusCode = 503;
    } else if (error.type === 'StripeAuthenticationError') {
      errorMessage = 'Erro de autenticação com o Stripe';
      statusCode = 500;
    }

    res.status(statusCode).json({
      success: false,
      error: errorMessage,
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// Verificar status do pagamento
router.get('/payment-status/:sessionId', async (req, res) => {
  try {
    const { sessionId } = req.params;
    
    // Buscar sessão no Stripe
    const session = await stripe.checkout.sessions.retrieve(sessionId);
    
    if (!session) {
      return res.status(404).json({
        success: false,
        error: 'Sessão de pagamento não encontrada'
      });
    }

    // Verificar se o pagamento foi bem-sucedido
    if (session.payment_status !== 'paid') {
      return res.status(400).json({
        success: false,
        error: 'Pagamento não foi concluído'
      });
    }

    // Buscar informações do produto/preço
    let planType = session.metadata?.planName || 'unknown';
    let billingCycle = session.metadata?.billingCycle || 'unknown';
    
    // Se não tiver nos metadados, buscar dos line items
    if (planType === 'unknown' || billingCycle === 'unknown') {
      try {
        const lineItems = await stripe.checkout.sessions.listLineItems(sessionId);
        if (lineItems.data.length > 0) {
          const item = lineItems.data[0];
          if (item.price) {
            const price = await stripe.prices.retrieve(item.price.id);
            if (price.product) {
              const product = await stripe.products.retrieve(price.product);
              planType = product.name || product.metadata?.plan_type || planType;
            }
            billingCycle = price.recurring?.interval || billingCycle;
          }
        }
      } catch (lineItemError) {
        console.warn('Erro ao buscar line items:', lineItemError);
      }
    }

    const paymentData = {
      sessionId: session.id,
      planType,
      billingCycle,
      customerEmail: session.customer_details?.email,
      amountTotal: session.amount_total,
      currency: session.currency,
      paymentStatus: session.payment_status,
      customerId: session.customer
    };

    res.json({
      success: true,
      data: paymentData
    });

  } catch (error) {
    console.error('Erro ao verificar status do pagamento:', error);
    res.status(500).json({
      success: false,
      error: 'Erro interno do servidor'
    });
  }
});

// Webhook do Stripe para processar eventos
router.post('/webhook', express.raw({ type: 'application/json' }), async (req, res) => {
  const sig = req.headers['stripe-signature'];
  const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET;

  let event;

  try {
    event = stripe.webhooks.constructEvent(req.body, sig, endpointSecret);
  } catch (err) {
    console.error('Erro na verificação do webhook:', err.message);
    return res.status(400).send(`Webhook Error: ${err.message}`);
  }

  // Processar eventos do Stripe
  try {
    switch (event.type) {
      case 'checkout.session.completed':
        const session = event.data.object;
        console.log('Checkout session completed:', session.id);
        // Aqui você pode adicionar lógica adicional se necessário
        break;

      case 'customer.subscription.created':
        const subscription = event.data.object;
        console.log('Subscription created:', subscription.id);
        break;

      case 'customer.subscription.updated':
        const updatedSubscription = event.data.object;
        console.log('Subscription updated:', updatedSubscription.id);
        break;

      case 'customer.subscription.deleted':
        const deletedSubscription = event.data.object;
        console.log('Subscription deleted:', deletedSubscription.id);
        break;

      case 'invoice.payment_succeeded':
        const invoice = event.data.object;
        console.log('Invoice payment succeeded:', invoice.id);
        break;

      case 'invoice.payment_failed':
        const failedInvoice = event.data.object;
        console.log('Invoice payment failed:', failedInvoice.id);
        break;

      default:
        console.log(`Evento não tratado: ${event.type}`);
    }

    res.json({ received: true });
  } catch (error) {
    console.error('Erro ao processar webhook:', error);
    res.status(500).json({ error: 'Erro ao processar webhook' });
  }
});

export default router;