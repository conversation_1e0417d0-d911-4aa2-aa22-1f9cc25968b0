#!/bin/bash

echo "========================================"
echo "   TESTE: INTEGRAÇÃO GEMINI AI"
echo "========================================"
echo

cd frontend

echo "[1/3] Verificando build..."
npm run build > build_test.log 2>&1

if [ $? -eq 0 ]; then
    echo "✅ Build OK - Integração compilando corretamente"
else
    echo "❌ Build FALHOU - Verificando erros:"
    cat build_test.log
    exit 1
fi

echo
echo "[2/3] Iniciando servidor..."
echo
echo "========================================"
echo "      🤖 TESTE DA INTEGRAÇÃO GEMINI"
echo "========================================"
echo
echo "PASSOS PARA TESTAR:"
echo
echo "1. CONFIGURE A API GEMINI:"
echo "   - Acesse: http://localhost:5173/ai/settings"
echo "   - Obtenha chave gratuita: https://makersuite.google.com/app/apikey"
echo "   - Selecione 'Google Gemini' como provedor"
echo "   - Cole sua chave API"
echo "   - Clique 'Testar' para validar"
echo "   - Salve as configurações"
echo
echo "2. TESTE A IA REAL:"
echo "   - Acesse: http://localhost:5173/ai"
echo "   - Vá para 'Análise de Sentimento'"
echo "   - Digite: 'Estou muito feliz com o atendimento!'"
echo "   - Clique 'Analisar Sentimento'"
echo "   - Deve usar IA real do Gemini"
echo
echo "3. TESTE CATEGORIZAÇÃO:"
echo "   - Vá para 'Categorização'"
echo "   - Digite: 'Tem um buraco na rua da minha casa'"
echo "   - Clique 'Categorizar Automaticamente'"
echo "   - Deve categorizar como 'Infraestrutura'"
echo
echo "4. COMPARE COM SIMULADO:"
echo "   - Volte para /ai/settings"
echo "   - Mude para 'Simulado (Gratuito)'"
echo "   - Teste novamente as funcionalidades"
echo "   - Compare os resultados"
echo
echo "========================================"
echo "      📊 INDICADORES DE SUCESSO"
echo "========================================"
echo
echo "✅ IA REAL FUNCIONANDO SE:"
echo "   - Análises mais precisas e detalhadas"
echo "   - Respostas em português natural"
echo "   - Confiança mais alta (>80%)"
echo "   - Categorização mais específica"
echo
echo "📊 SIMULADO FUNCIONANDO SE:"
echo "   - Análises baseadas em palavras-chave"
echo "   - Respostas mais simples"
echo "   - Confiança moderada (60-70%)"
echo "   - Categorização básica"
echo
echo "========================================"
echo "      🚀 SERVIDOR INICIANDO..."
echo "========================================"
echo
echo "Pressione Ctrl+C para parar o servidor"
echo

npm run dev

echo
cd ..
read -p "Pressione Enter para continuar..."
