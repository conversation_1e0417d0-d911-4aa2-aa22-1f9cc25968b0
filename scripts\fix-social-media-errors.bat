@echo off
echo ========================================
echo   CORRIGINDO ERROS DA SOCIAL MEDIA
echo ========================================
echo.

cd frontend

echo [1/2] Verificando se o projeto compila...
npm run build 2>&1 | findstr /C:"error" /C:"Error" /C:"ERROR"

if %errorlevel% equ 0 (
    echo ❌ Erros encontrados na compilacao
    echo.
    echo Executando build completo para ver erros:
    npm run build
) else (
    echo ✅ Nenhum erro encontrado!
    echo.
    echo [2/2] Testando servidor de desenvolvimento...
    timeout /t 3 /nobreak > nul
    echo Iniciando servidor...
)

cd ..
pause
